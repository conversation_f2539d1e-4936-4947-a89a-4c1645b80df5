# 医学写作智能体 - 缩略语提取系统

## 项目概述

本项目是一个专门用于医学文档（Clinical Study Report, CSR）的缩略语提取和标准化系统。系统能够从医学文档的正文部分和TFL（Tables, Figures, Listings）表格部分智能提取缩略语，并进行标准化处理。

## 核心功能

### 1. 双源缩略语提取
- **正文部分（Main Content）**：从医学报告的正文中提取缩略语
- **TFL表格部分（TFL Tables）**：从表格、图表、列表中提取缩略语
- **智能合并**：自动合并两个来源的结果，避免重复

### 2. 智能文本处理
- **语义分割**：将长文本按语义边界分割为适合处理的片段
- **括号识别**：识别各种括号格式中的缩略语（中英文括号）
- **大写字母检测**：只处理包含大写英文字母的潜在缩略语

### 3. 知识库增强
- **RAG检索**：结合医学知识库进行缩略语识别和补全
- **上下文理解**：基于上下文信息补全缺失的中文术语或英文全称

### 4. 标准化处理
- **术语规范化**：使用标准医学术语库规范化提取结果
- **优先级处理**：正文来源（main）优先于表格来源（tfl）
- **去重机制**：基于英文简称去重，保留最高优先级的条目

### 5. 质量控制
- **异常值过滤**：过滤掉中文术语不含中文或英文简称不含英文的异常数据
- **零计数过滤**：只返回在原文中实际出现的术语
- **正则匹配**：使用词边界匹配确保准确性

## 技术架构

### 工作流程设计
```
输入数据 → 文本预处理 → 并行提取 → 知识增强 → 合并去重 → 标准化 → 质量控制 → 输出结果
```

### 核心组件

#### 1. 文本预处理模块
- 清理特殊字符（ZWSP、BOM等）
- 按语义边界分割长文本
- 表格数据格式化为文本

#### 2. 缩略语提取模块
- LLM驱动的智能提取
- 支持多种括号格式识别
- 上下文相关的术语补全

#### 3. 知识检索模块
- 基于向量相似度的知识检索
- 关键词和语义双重匹配
- 医学术语标准化

#### 4. 后处理模块
- 优先级合并算法
- 多维度去重机制
- 异常值检测和过滤

## 使用方法

### 输入格式
```json
{
  "main_content": ["正文段落1", "正文段落2", ...],
  "tfl_table_content": [
    {
      "name": "表格名称",
      "table_summary": {"headers": ["列1", "列2", ...]},
      "footnote": "脚注信息"
    },
    ...
  ]
}
```

### 输出格式
```json
{
  "result": [
    {
      "中文术语": "全分析集",
      "英文全称": "Full Analysis Set",
      "英文简称": "FAS",
      "source": "main",
      "count": 13
    },
    ...
  ]
}
```

## 核心算法

### 1. 文本分割算法
```python
def split_text_by_semantic_boundaries(text: str, target_length: int = 200) -> list[str]:
    # 按换行符和句号等语义边界分割
    # 确保每个片段长度适中且语义完整
```

### 2. 缩略语匹配算法
```python
# 使用词边界正则表达式确保精确匹配
pattern = rf"\b{re.escape(abbr)}\b"
count = len(re.findall(pattern, text, flags=re.IGNORECASE))
```

### 3. 优先级合并算法
```python
# 基于来源优先级的去重合并
if abbr not in abbreviation_map:
    abbreviation_map[abbr] = entry
elif entry.get("source") == "main" and abbreviation_map[abbr].get("source") != "main":
    abbreviation_map[abbr] = entry
```

### 4. 异常值检测
```python
def has_chinese(text: str) -> bool:
    return bool(re.search(r'[\u4e00-\u9fff]', text))

def has_english(text: str) -> bool:
    return bool(re.search(r'[a-zA-Z]', text))
```

## 特色功能

### 1. 中英文混合处理
- 支持中英文混合的医学文档
- 智能识别各种括号格式
- 处理中文标点符号

### 2. 医学领域专业化
- 内置医学术语标准库
- 支持医学缩略语的特殊格式
- 理解医学文档的结构特点

### 3. 高精度提取
- 基于大语言模型的智能理解
- 结合知识库的准确补全
- 多重验证机制确保质量

### 4. 可扩展架构
- 模块化设计便于扩展
- 支持自定义术语库
- 灵活的配置选项

## 性能优化

- **并行处理**：支持多线程并行提取
- **缓存机制**：知识库检索结果缓存
- **批量处理**：支持大文档的分批处理
- **内存优化**：流式处理减少内存占用

## 应用场景

1. **临床研究报告（CSR）编写**
2. **医学文献缩略语标准化**
3. **药物研发文档处理**
4. **医学翻译辅助**
5. **医学知识库构建**

## 技术栈

- **Python 3.8+**
- **大语言模型**：支持多种LLM API
- **向量检索**：BGE-M3 embedding模型
- **知识库**：医学术语标准库
- **工作流引擎**：Dify workflow

## 文件结构

```
├── README.md                           # 项目说明文档
├── CSR 写作智能体-缩略语提取.yml        # Dify工作流配置
├── 最终返回.py                         # 核心处理逻辑
└── 周报总结.md                         # 项目周报
```

## 更新日志

### v1.0.0 (2024-07-28)
- ✅ 实现双源缩略语提取
- ✅ 添加知识库增强功能
- ✅ 实现优先级合并机制
- ✅ 添加异常值过滤
- ✅ 完善质量控制体系

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。请确保：
1. 代码符合PEP 8规范
2. 添加适当的测试用例
3. 更新相关文档

## 许可证

本项目采用MIT许可证。