app:
  description: 提取文章中的缩略语
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: CSR 写作智能体-缩略语提取
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.16@77274df8fe2632cac66bfd153fcc75aa5e96abbe92b5c611b8984ad9f4cd4457
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 100
        image_file_size_limit: 100
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: code
      id: 1732535612826-source-1753669997187-target
      selected: false
      source: '1732535612826'
      sourceHandle: source
      target: '1753669997187'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        sourceType: iteration-start
        targetType: llm
      id: 1732535749965start-source-1753670672567-target
      selected: false
      source: 1732535749965start
      sourceHandle: source
      target: '1753670672567'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        sourceType: llm
        targetType: knowledge-retrieval
      id: 1753670672567-source-1753670831256-target
      selected: false
      source: '1753670672567'
      sourceHandle: source
      target: '1753670831256'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: code
      id: 1753669997187-source-1753671791115-target
      selected: false
      source: '1753669997187'
      sourceHandle: source
      target: '1753671791115'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: iteration
      id: 1753671791115-source-1732535749965-target
      selected: false
      source: '1753671791115'
      sourceHandle: source
      target: '1732535749965'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1753670831256-source-1732535790685-target
      selected: false
      source: '1753670831256'
      sourceHandle: source
      target: '1732535790685'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: code
      id: 1753669997187-source-1753673413775-target
      selected: false
      source: '1753669997187'
      sourceHandle: source
      target: '1753673413775'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        sourceType: iteration-start
        targetType: llm
      id: 1753673455541start-source-1753673455541017536734555411-target
      selected: false
      source: 1753673455541start
      sourceHandle: source
      target: '1753673455541017536734555411'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        sourceType: llm
        targetType: knowledge-retrieval
      id: 1753673455541017536734555411-source-1753673455541017536734555412-target
      selected: false
      source: '1753673455541017536734555411'
      sourceHandle: source
      target: '1753673455541017536734555412'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1753673455541017536734555412-source-1753673455541017536734555410-target
      selected: false
      source: '1753673455541017536734555412'
      sourceHandle: source
      target: '1753673455541017536734555410'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: code
        targetType: iteration
      id: 1753673413775-source-17536734555410-target
      selected: false
      source: '1753673413775'
      sourceHandle: source
      target: '17536734555410'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: code
      id: 1753669997187-source-17536744443700-target
      source: '1753669997187'
      sourceHandle: source
      target: '17536744443700'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: code
      id: 17536744443700-source-1753674630551-target
      source: '17536744443700'
      sourceHandle: source
      target: '1753674630551'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: code
      id: 1753673470605-source-1753675304191-target
      source: '1753673470605'
      sourceHandle: source
      target: '1753675304191'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: end
      id: 1753675304191-source-1753684498784-target
      source: '1753675304191'
      sourceHandle: source
      target: '1753684498784'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: iteration
        targetType: template-transform
      id: 1732535749965-source-1753686202400-target
      source: '1732535749965'
      sourceHandle: source
      target: '1753686202400'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: code
      id: 1753686202400-source-1753673470605-target
      source: '1753686202400'
      sourceHandle: source
      target: '1753673470605'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: iteration
        targetType: template-transform
      id: 17536734555410-source-1753686211748-target
      source: '17536734555410'
      sourceHandle: source
      target: '1753686211748'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: code
      id: 1753686211748-source-1753673470605-target
      source: '1753686211748'
      sourceHandle: source
      target: '1753673470605'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: code
      id: 1753674630551-source-1753673470605-target
      source: '1753674630551'
      sourceHandle: source
      target: '1753673470605'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - allowed_file_extensions:
          - .docx
          allowed_file_types:
          - document
          allowed_file_upload_methods:
          - local_file
          label: 正文部分list[str]
          max_length: 10000000
          options: []
          required: true
          type: paragraph
          variable: main_content
        - label: tfl 表格部分list[str]
          max_length: 10000000
          options: []
          required: true
          type: paragraph
          variable: tfl_table_content
      height: 114
      id: '1732535612826'
      position:
        x: -477.4201904230434
        y: 289.8188854216004
      positionAbsolute:
        x: -477.4201904230434
        y: 289.8188854216004
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        error_handle_mode: terminated
        height: 202
        is_parallel: true
        iterator_input_type: array[string]
        iterator_selector:
        - '1753671791115'
        - result
        output_selector:
        - '1732535790685'
        - text
        output_type: array[string]
        parallel_nums: 8
        selected: false
        start_node_id: 1732535749965start
        title: 正文部分-迭代
        type: iteration
        width: 1555.031078979462
      height: 202
      id: '1732535749965'
      position:
        x: 948.2679795975826
        y: 7.197310621897515
      positionAbsolute:
        x: 948.2679795975826
        y: 7.197310621897515
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1555
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1732535749965start
      parentId: '1732535749965'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 972.2679795975826
        y: 75.19731062189751
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: true
          variable_selector:
          - '1753670831256'
          - result
        desc: ''
        isInIteration: true
        iteration_id: '1732535749965'
        model:
          completion_params: {}
          mode: chat
          name: yiya-ds-v3
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: cf311baf-c437-43f5-8d15-7c8710e77283
          role: system
          text: ''
        - id: 474dc293-7694-4588-b92d-76c3deab4cfc
          role: user
          text: "## 角色设定  \n你是一名经验丰富的医学缩略语抽取专员，擅长从中文医学文本中识别并提取标准化的中英文缩略语对。你的任务是根据括号中出现的大写英文缩略词，结合上下文信息，提取完整的术语组合，并输出结构化\
            \ JSON 结果。\n\n## 任务目标  \n请从输入的句子中，提取所有符合下列规则的缩略语组合，并逐条输出以下三个字段：\n\n- \"\
            中文术语\"：对应的医学中文名称（如缺失，需结合上下文合理补充）\n- \"英文全称\"：英文术语全称（如缺失，需基于专业常识补充）\n-\
            \ \"英文简称\"：括号中的英文大写缩略词（必须提取，保持原样大写）\n\n参考资料：\n{{#context#}}\n\n## 缩略语特征规则\
            \  \n\n1. 括号结构格式示例：\n   - 中文术语（英文全称，英文简称）  \n     示例：突破玻璃膜（Bruch membrane，BM）\n\
            \   - 中文术语（英文简称）  \n     示例：天冬氨酸转氨酶（AST）\n   - 英文全称（英文简称）  \n     示例：Scottish\
            \ Intercollegiate Guidelines Network（SIGN）\n\n2. 补全说明：\n   - 括号内如出现多个术语（如“XX，XX”），需按语义正确拆分，分别输出。\n\
            \   - 缺失字段需结合上下文或专业医学常识补全，**不能留空字符串或为 null**。\n   - 所有输出字段必须使用英文半角双引号包裹；每条结果单独一行，输出为\
            \ JSON Line 格式。\n\n## 示例说明\n\n### 示例 1：中英文全称+简称\n\n输入：\n```\n2型起源于脉络膜，突破玻璃膜（Bruch\
            \ membrane，BM）和RPE层在视网膜下增生。\n```\n\n输出：\n```\n{\"中文术语\": \"突破玻璃膜\", \"\
            英文全称\": \"Bruch membrane\", \"英文简称\": \"BM\"}\n```\n\n### 示例 2：仅包含英文简称，需补全中英文全称\n\
            \n输入：\n```\n天冬氨酸转氨酶（AST）、丙氨酸转氨酶（ALT）\n```\n\n输出：\n```\n{\"中文术语\": \"天冬氨酸转氨酶\"\
            , \"英文全称\": \"Aspartate Transaminase\", \"英文简称\": \"AST\"}\n{\"中文术语\"\
            : \"丙氨酸转氨酶\", \"英文全称\": \"Alanine Aminotransferase\", \"英文简称\": \"ALT\"\
            }\n```\n\n### 示例 3：仅英文全称+简称，需补全中文术语\n\n输入：\n```\nScottish Intercollegiate\
            \ Guidelines Network（SIGN）\n```\n\n输出：\n```\n{\"中文术语\": \"苏格兰国家卒中指南\"\
            , \"英文全称\": \"Scottish Intercollegiate Guidelines Network\", \"英文简称\"\
            : \"SIGN\"}\n```\n\n## 输出格式  \n\n请严格按以下格式输出，每行一个缩略语组合对象，不解释、不换行、不输出多余内容：\n\
            \n```json\n{\"中文术语\": \"XXX\", \"英文全称\": \"YYY\", \"英文简称\": \"ZZZ\"}\n\
            ```\n\n## 待处理医学文本  \n{{#1732535749965.item#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        structured_output_enabled: false
        title: LLM-提取缩略语-正文
        type: llm
        variables: []
        vision:
          enabled: false
      height: 117
      id: '1732535790685'
      parentId: '1732535749965'
      position:
        x: 881.9484998321248
        y: 65
      positionAbsolute:
        x: 1830.2164794297073
        y: 72.19731062189751
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    - data:
        code: "import json\nimport re\n\n\ndef clean_text(text: str) -> str:\n   \
          \ \"\"\"清理文本，去除无用的ZWSP等字符\"\"\"\n    if not isinstance(text, str):\n   \
          \     return str(text)\n\n    # 去除零宽空格 (ZWSP) 和其他不可见字符\n    text = text.replace(\"\
          \\u200b\", \"\")  # ZWSP\n    text = text.replace(\"\\ufeff\", \"\")  #\
          \ BOM\n    text = text.replace(\"\\u200c\", \"\")  # ZWNJ\n    text = text.replace(\"\
          \\u200d\", \"\")  # ZWJ\n\n    # 去除多余的空白字符和换行符\n    text = re.sub(r\"\\\
          s+\", \" \", text)\n    text = text.strip()\n\n    return text\n\n\ndef\
          \ split_text_by_semantic_boundaries(text: str, target_length: int = 200)\
          \ -> list[str]:\n    \"\"\"按语义边界分割文本，每段大约target_length字符，确保以换行符或句号结尾\"\"\
          \"\n    if not text:\n        return []\n\n    # 首先按换行符分割\n    lines = text.split(\"\
          \\n\")\n    result = []\n    current_chunk = \"\"\n\n    for line in lines:\n\
          \        line = line.strip()\n        if not line:\n            continue\n\
          \n        # 如果当前行本身就很长，需要进一步分割\n        if len(line) > target_length * 1.5:\n\
          \            # 如果当前chunk不为空，先保存（确保以换行符结尾）\n            if current_chunk:\n\
          \                if not current_chunk.endswith(\"\\n\"):\n             \
          \       current_chunk += \"\\n\"\n                result.append(current_chunk.rstrip())\n\
          \                current_chunk = \"\"\n\n            # 按中文句号分割长行\n     \
          \       sentences = line.split(\"。\")\n            temp_chunk = \"\"\n\n\
          \            for i, sentence in enumerate(sentences):\n                sentence\
          \ = sentence.strip()\n                if not sentence:\n               \
          \     continue\n\n                # 如果不是最后一个句子，加回句号\n                if\
          \ i < len(sentences) - 1:\n                    sentence += \"。\"\n\n   \
          \             # 检查添加这个句子后是否超长\n                if temp_chunk and len(temp_chunk\
          \ + sentence) > target_length:\n                    # 确保以句号结尾\n        \
          \            if not temp_chunk.endswith(\"。\"):\n                      \
          \  temp_chunk += \"。\"\n                    result.append(temp_chunk.strip())\n\
          \                    temp_chunk = sentence\n                else:\n    \
          \                temp_chunk = temp_chunk + sentence if temp_chunk else sentence\n\
          \n            # 保存剩余的temp_chunk\n            if temp_chunk:\n          \
          \      current_chunk = temp_chunk\n        else:\n            # 检查添加这一行后是否超长\n\
          \            test_chunk = current_chunk + \"\\n\" + line if current_chunk\
          \ else line\n            if len(test_chunk) > target_length:\n         \
          \       # 保存当前chunk，确保语义完整\n                if current_chunk:\n        \
          \            # 检查当前chunk是否以合适的边界结尾\n                    if not (current_chunk.endswith(\"\
          。\") or current_chunk.endswith(\"\\n\")):\n                        # 如果当前行以句号结尾，可以包含进来\n\
          \                        if line.endswith(\"。\") and len(test_chunk) <=\
          \ target_length * 1.2:\n                            current_chunk = test_chunk\n\
          \                            continue\n                    result.append(current_chunk.strip())\n\
          \                current_chunk = line\n            else:\n             \
          \   current_chunk = test_chunk\n\n    # 保存最后的chunk\n    if current_chunk:\n\
          \        result.append(current_chunk.strip())\n\n    # 后处理：确保每个chunk都以合适的边界结尾\n\
          \    processed_result = []\n    for chunk in result:\n        if chunk and\
          \ not (chunk.endswith(\"。\") or chunk.endswith(\"\\n\")):\n            #\
          \ 如果不以句号或换行符结尾，尝试在合适的位置添加\n            if \"。\" in chunk:\n            \
          \    # 找到最后一个句号的位置\n                last_period = chunk.rfind(\"。\")\n \
          \               if last_period > len(chunk) * 0.7:  # 如果句号在后70%的位置\n   \
          \                 chunk = chunk[: last_period + 1]\n            # 如果仍然不以句号结尾，保持原样（可能是列表项等）\n\
          \        processed_result.append(chunk)\n\n    return processed_result\n\
          \n\ndef convert_main_content_to_text_list(main_content_list: list[str],\
          \ target_length: int = 200) -> list[str]:\n    \"\"\"将main_content列表转换为按语义分割的文本字符串列表\"\
          \"\"\n    # 清理每个条目并过滤空内容\n    cleaned_items = []\n    for item in main_content_list:\n\
          \        cleaned = clean_text(item)\n        if cleaned and cleaned not\
          \ in [\"\\u200b\", \"\"]:  # 过滤空内容和特殊字符\n            cleaned_items.append(cleaned)\n\
          \n    # 用换行符连接所有内容\n    merged_content = \"\\n\".join(cleaned_items)\n\n\
          \    # 按语义边界分割\n    text_chunks = split_text_by_semantic_boundaries(merged_content,\
          \ target_length)\n\n    return text_chunks\n\n\ndef format_table_as_text(table:\
          \ dict) -> str:\n    \"\"\"将单个表格对象转换为文本格式\"\"\"\n    lines = []\n\n    #\
          \ 表头名称\n    name = table.get(\"name\", \"\")\n    if name:\n        lines.append(f\"\
          表头名称：{clean_text(name)}\")\n\n    # 表格的首行名称（headers）\n    headers = []\n\
          \n    # 优先从table_summary中获取headers（优化后的数据）\n    if \"table_summary\" in\
          \ table and \"headers\" in table[\"table_summary\"]:\n        headers =\
          \ table[\"table_summary\"][\"headers\"]\n    # 如果没有table_summary，尝试从原始tblData中获取第一行作为headers\n\
          \    elif \"tblData\" in table and table[\"tblData\"] and len(table[\"tblData\"\
          ]) > 0:\n        headers = table[\"tblData\"][0]  # 第一行通常是表头\n\n    if headers:\n\
          \        # 清理headers并用 | 分隔\n        clean_headers = [clean_text(header)\
          \ for header in headers if header and str(header).strip()]\n        if clean_headers:\n\
          \            headers_text = \" | \".join(clean_headers)\n            lines.append(f\"\
          表格的首行名称：{headers_text}\")\n\n    # 脚注\n    footnote = table.get(\"footnote\"\
          , \"\")\n    if footnote:\n        clean_footnote = clean_text(footnote)\n\
          \        lines.append(f\"脚注：{clean_footnote}\")\n\n    # 如果没有任何内容，至少保留表头名称\n\
          \    if not lines and name:\n        lines.append(f\"表头名称：{clean_text(name)}\"\
          )\n\n    return \"\\n\".join(lines)\n\n\ndef convert_tfl_content_to_text_list(tfl_list:\
          \ list[dict]) -> list[str]:\n    \"\"\"将TFL表格列表转换为文本字符串列表\"\"\"\n    text_tables\
          \ = []\n    for i, table in enumerate(tfl_list):\n        try:\n       \
          \     table_text = format_table_as_text(table)\n            if table_text.strip():\
          \  # 只添加非空的文本\n                text_tables.append(table_text)\n        \
          \    else:\n                # 如果格式化失败，至少保留基本信息\n                basic_text\
          \ = f\"表头名称：{table.get('name', f'表格{i + 1}')}\"\n                text_tables.append(basic_text)\n\
          \        except Exception as e:\n            # 创建一个基本的文本表示\n           \
          \ basic_text = f\"表头名称：{table.get('name', f'表格{i + 1}')}\"\n           \
          \ text_tables.append(basic_text)\n\n    return text_tables\n\n\ndef main(arg1:\
          \ str, arg2: str) -> dict:\n    \"\"\"\n    主函数：处理两个字符串参数，返回指定格式的JSON数据\n\
          \    \n    Args:\n        arg1 (str): main_content的JSON字符串\n        arg2\
          \ (str): tfl_table_content的JSON字符串\n    \n    Returns:\n        dict: 包含main_content和tfl_table_content列表的字典\n\
          \        格式: {\n            \"main_content\": [str],\n            \"tfl_table_content\"\
          : [str]\n        }\n    \"\"\"\n    try:\n        # 解析main_content\n   \
          \     main_content_list = json.loads(arg1)\n        \n        # 解析tfl_table_content\n\
          \        tfl_list = json.loads(arg2)\n        \n        # 转换main_content为语义分割的文本字符串列表\n\
          \        main_content_text_list = convert_main_content_to_text_list(main_content_list)\n\
          \        \n        # 转换tfl_table_content为文本字符串列表\n        tfl_content_text_list\
          \ = convert_tfl_content_to_text_list(tfl_list)\n        \n        # 返回指定格式的结果\n\
          \        result = {\n            \"main_content\": main_content_text_list,\n\
          \            \"tfl_table_content\": tfl_content_text_list,\n        }\n\
          \        \n        return result\n        \n    except json.JSONDecodeError\
          \ as e:\n        raise ValueError(f\"JSON解析错误: {e}\")\n    except Exception\
          \ as e:\n        raise RuntimeError(f\"处理过程中发生错误: {e}\")\n\n"
        code_language: python3
        desc: ''
        outputs:
          main_content:
            children: null
            type: array[string]
          tfl_table_content:
            children: null
            type: array[string]
        selected: false
        title: 代码执行 4
        type: code
        variables:
        - value_selector:
          - '1732535612826'
          - main_content
          value_type: string
          variable: arg1
        - value_selector:
          - '1732535612826'
          - tfl_table_content
          value_type: string
          variable: arg2
      height: 54
      id: '1753669997187'
      position:
        x: -7.701962636554072
        y: 337.0517705931656
      positionAbsolute:
        x: -7.701962636554072
        y: 337.0517705931656
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: yiya-ds-v3
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 829ef064-31df-4e62-9c74-d02810092a26
          role: system
          text: ''
        - id: ce75b2ec-bbee-4ee1-b813-f83f43bae4e1
          role: user
          text: "<instruction>\n从输入的字符串中提取**出现在包含大写英文字母的词附近**的医学专有术语和英文缩略词。请严格按照以下步骤操作：\n\
            \n1. 仔细阅读输入字符串，定位所有包含大写英文字母的词（如 MRI、CT、TNF-α）。\n2. 仅从这些词**前后相邻的语义区域**中提取可能的医学专有术语或相关描述\n\
            3. 英文缩略词本身（如 CT、MRI）也需提取，并保持大写。\n4. 确保提取的术语完整，不可拆词或截断，不提取无关普通名词。\n5. 如果没有，直接输出字符串“空”\n\
            \n</instruction>\n\n<examples>\n<example>\n输入：MRI检查显示脑部有异常信号，建议进一步做CT扫描。\n\
            输出：MRI 脑部 CT 扫描\n</example>\n\n<example>\n输入：服用阿司匹林可以降低心肌梗死的风险，特别是在已接受PCI治疗的患者中。\n\
            输出： PCI\n</example>\n\n<example>\n输入：使用TNF-α抑制剂治疗类风湿性关节炎的效果较好。\n输出：TNF-α\
            \ 抑制剂 \n</example>\n</examples>\n\n当前输入：\n输入：{{#1732535749965.item#}}\n\
            输出："
        selected: false
        title: 提取关键词-正文
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1753670672567'
      parentId: '1732535749965'
      position:
        x: 77.86467232497262
        y: 65
      positionAbsolute:
        x: 1026.1326519225552
        y: 72.19731062189751
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        dataset_ids:
        - JT9p2K23awtPRdpAVFnEbyK3l3qPW3zR3aM1qsW83B7uSlFiL3BJphRnMLUbH5B+
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1732535749965'
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: weighted_score
          reranking_model:
            model: BAAI/bge-reranker-v2-m3
            provider: langgenius/openai_api_compatible/openai_api_compatible
          score_threshold: null
          top_k: 2
          weights:
            keyword_setting:
              keyword_weight: 0.5
            vector_setting:
              embedding_model_name: bge-m3
              embedding_provider_name: langgenius/openai_api_compatible/openai_api_compatible
              vector_weight: 0.5
        query_variable_selector:
        - '1753670672567'
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索-正文
        type: knowledge-retrieval
      height: 54
      id: '1753670831256'
      parentId: '1732535749965'
      position:
        x: 339.8369861612032
        y: 66
      positionAbsolute:
        x: 1288.1049657587857
        y: 73.19731062189751
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        code: "import re\n\ndef main(input_list: list[str]) -> dict:\n    output_list\
          \ = []\n\n    for s in input_list:\n        # 查找所有中英文括号对中的内容\n        matches\
          \ = re.findall(r'[(（](.*?)[)）]', s)\n        # 如果至少一个括号内的内容含有大写英文字母，则保留该字符串\n\
          \        if any(re.search(r'[A-Z]', m) for m in matches):\n            output_list.append(s)\n\
          \n    return {'result': output_list}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: 找到包含英文大写字母的句子-正文
        type: code
        variables:
        - value_selector:
          - '1753669997187'
          - main_content
          value_type: array[string]
          variable: input_list
      height: 54
      id: '1753671791115'
      position:
        x: 480.4087780584814
        y: 25.615248903826966
      positionAbsolute:
        x: 480.4087780584814
        y: 25.615248903826966
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import re\n\ndef main(input_list: list[str]) -> dict:\n    output_list\
          \ = []\n\n    for s in input_list:\n        # 查找所有中英文括号对中的内容\n        matches\
          \ = re.findall(r'[(（](.*?)[)）]', s)\n        # 如果至少一个括号内的内容含有大写英文字母，则保留该字符串\n\
          \        if any(re.search(r'[A-Z]', m) for m in matches):\n            output_list.append(s)\n\
          \n    return {'result': output_list}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: 把所有句子拼接起来
        type: code
        variables:
        - value_selector:
          - '1753669997187'
          - tfl_table_content
          value_type: array[string]
          variable: input_list
      height: 54
      id: '1753673413775'
      position:
        x: 471.59591806056665
        y: 385.3881787022858
      positionAbsolute:
        x: 471.59591806056665
        y: 385.3881787022858
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_handle_mode: terminated
        height: 202
        is_parallel: true
        iterator_input_type: array[string]
        iterator_selector:
        - '1753673413775'
        - result
        output_selector:
        - '1753673455541017536734555410'
        - text
        output_type: array[string]
        parallel_nums: 8
        selected: false
        start_node_id: 1753673455541start
        title: tfl 部分-迭代
        type: iteration
        width: 1555.031078979462
      height: 202
      id: '17536734555410'
      position:
        x: 948.2679795975826
        y: 394.7489353395631
      positionAbsolute:
        x: 948.2679795975826
        y: 394.7489353395631
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1555
      zIndex: 1
    - data:
        context:
          enabled: true
          variable_selector:
          - '1753673455541017536734555412'
          - result
        desc: ''
        isInIteration: true
        iteration_id: '17536734555410'
        model:
          completion_params: {}
          mode: chat
          name: yiya-ds-v3
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: cf311baf-c437-43f5-8d15-7c8710e77283
          role: system
          text: ''
        - id: 474dc293-7694-4588-b92d-76c3deab4cfc
          role: user
          text: "## 角色设定  \n你是一名经验丰富的医学缩略语抽取专员，擅长从中文医学文本中识别并提取标准化的中英文缩略语对。你的任务是根据括号中出现的大写英文缩略词，结合上下文信息，提取完整的术语组合，并输出结构化\
            \ JSON 结果。\n\n## 任务目标  \n请从输入的句子中，提取所有符合下列规则的缩略语组合，并逐条输出以下三个字段：\n\n- \"\
            中文术语\"：对应的医学中文名称（如缺失，需结合上下文合理补充）\n- \"英文全称\"：英文术语全称（如缺失，需基于专业常识补充）\n-\
            \ \"英文简称\"：括号中的英文大写缩略词（必须提取，保持原样大写）\n\n参考资料：\n{{#context#}}\n\n## 缩略语特征规则\
            \  \n\n1. 括号结构格式示例：\n   - 中文术语（英文全称，英文简称）  \n     示例：突破玻璃膜（Bruch membrane，BM）\n\
            \   - 中文术语（英文简称）  \n     示例：天冬氨酸转氨酶（AST）\n   - 英文全称（英文简称）  \n     示例：Scottish\
            \ Intercollegiate Guidelines Network（SIGN）\n\n2. 补全说明：\n   - 括号内如出现多个术语（如“XX，XX”），需按语义正确拆分，分别输出。\n\
            \   - 缺失字段需结合上下文或专业医学常识补全，**不能留空字符串或为 null**。\n   - 所有输出字段必须使用英文半角双引号包裹；每条结果单独一行，输出为\
            \ JSON Line 格式。\n\n## 示例说明\n\n### 示例 1：中英文全称+简称\n\n输入：\n```\n2型起源于脉络膜，突破玻璃膜（Bruch\
            \ membrane，BM）和RPE层在视网膜下增生。\n```\n\n输出：\n```\n{\"中文术语\": \"突破玻璃膜\", \"\
            英文全称\": \"Bruch membrane\", \"英文简称\": \"BM\"}\n```\n\n### 示例 2：仅包含英文简称，需补全中英文全称\n\
            \n输入：\n```\n天冬氨酸转氨酶（AST）、丙氨酸转氨酶（ALT）\n```\n\n输出：\n```\n{\"中文术语\": \"天冬氨酸转氨酶\"\
            , \"英文全称\": \"Aspartate Transaminase\", \"英文简称\": \"AST\"}\n{\"中文术语\"\
            : \"丙氨酸转氨酶\", \"英文全称\": \"Alanine Aminotransferase\", \"英文简称\": \"ALT\"\
            }\n```\n\n### 示例 3：仅英文全称+简称，需补全中文术语\n\n输入：\n```\nScottish Intercollegiate\
            \ Guidelines Network（SIGN）\n```\n\n输出：\n```\n{\"中文术语\": \"苏格兰国家卒中指南\"\
            , \"英文全称\": \"Scottish Intercollegiate Guidelines Network\", \"英文简称\"\
            : \"SIGN\"}\n```\n\n## 输出格式  \n\n请严格按以下格式输出，每行一个缩略语组合对象，不解释、不换行、不输出多余内容：\n\
            \n```json\n{\"中文术语\": \"XXX\", \"英文全称\": \"YYY\", \"英文简称\": \"ZZZ\"}\n\
            ```\n\n## 待处理医学文本  \n{{#17536734555410.item#}}"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1049
        selected: false
        title: LLM-提取缩略语-tfl
        type: llm
        variables: []
        vision:
          enabled: false
      height: 117
      id: '1753673455541017536734555410'
      parentId: '17536734555410'
      position:
        x: 825.7848153955722
        y: 65
      positionAbsolute:
        x: 1774.0527949931547
        y: 459.7489353395631
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '17536734555410'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: yiya-ds-v3
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 829ef064-31df-4e62-9c74-d02810092a26
          role: system
          text: ''
        - id: ce75b2ec-bbee-4ee1-b813-f83f43bae4e1
          role: user
          text: "<instruction>\n从输入的字符串中提取**出现在包含大写英文字母的词附近**的医学专有术语和英文缩略词。请严格按照以下步骤操作：\n\
            \n1. 仔细阅读输入字符串，定位所有包含大写英文字母的词（如 MRI、CT、TNF-α）。\n2. 仅从这些词**前后相邻的语义区域**中提取可能的医学专有术语或相关描述\n\
            3. 英文缩略词本身（如 CT、MRI）也需提取，并保持大写。\n4. 确保提取的术语完整，不可拆词或截断，不提取无关普通名词。\n5. 如果没有，直接输出字符串“空”\n\
            \n</instruction>\n\n<examples>\n<example>\n输入：MRI检查显示脑部有异常信号，建议进一步做CT扫描。\n\
            输出：MRI 脑部 CT 扫描\n</example>\n\n<example>\n输入：服用阿司匹林可以降低心肌梗死的风险，特别是在已接受PCI治疗的患者中。\n\
            输出： PCI\n</example>\n\n<example>\n输入：使用TNF-α抑制剂治疗类风湿性关节炎的效果较好。\n输出：TNF-α\
            \ 抑制剂 \n</example>\n</examples>\n\n当前输入：\n输入：{{#17536734555410.item#}}\n\
            输出："
        selected: false
        title: 提取关键词-tfl
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1753673455541017536734555411'
      parentId: '17536734555410'
      position:
        x: 72.26206110640851
        y: 65
      positionAbsolute:
        x: 1020.5300407039911
        y: 459.7489353395631
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        dataset_ids:
        - JT9p2K23awtPRdpAVFnEbyK3l3qPW3zR3aM1qsW83B7uSlFiL3BJphRnMLUbH5B+
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '17536734555410'
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: weighted_score
          reranking_model:
            model: BAAI/bge-reranker-v2-m3
            provider: langgenius/openai_api_compatible/openai_api_compatible
          score_threshold: null
          top_k: 2
          weights:
            keyword_setting:
              keyword_weight: 0.5
            vector_setting:
              embedding_model_name: bge-m3
              embedding_provider_name: langgenius/openai_api_compatible/openai_api_compatible
              vector_weight: 0.5
        query_variable_selector:
        - '1753673455541017536734555411'
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索-tfl
        type: knowledge-retrieval
      height: 54
      id: '1753673455541017536734555412'
      parentId: '17536734555410'
      position:
        x: 385.4699797659018
        y: 66
      positionAbsolute:
        x: 1333.7379593634844
        y: 460.7489353395631
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1753673455541start
      parentId: '17536734555410'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 972.2679795975826
        y: 462.7489353395631
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        code: "import json\nimport re\n\ndef main(arg1, arg2) -> dict:\n    \"\"\"\
          \n    解析、合并、去重并标记来源两个列表中的JSON字符串。\n\n    Args:\n      arg1: 来源为 \"main\"\
          \ 的原始字符串列表。\n      arg2: 来源为 \"tfl\" 的原始字符串列表。\n\n    Returns:\n      一个包含\
          \ \"result\" 键的字典，其值为处理后的对象列表。\n    \"\"\"\n\n    # 使用字典来存储唯一的对象，键是对象的规范化字符串表示，值是带来源信息的对象本身。\n\
          \    # 这样可以高效地去重并实现来源优先逻辑。\n    processed_objects = {}\n\n    def parse_and_process(raw_strings:\
          \ list[str], source: str):\n        \"\"\"\n        一个内部辅助函数，用于解析字符串列表并填充\
          \ processed_objects 字典。\n        \"\"\"\n        for raw_str in raw_strings:\n\
          \            # 移除常见的Markdown代码块标记\n            cleaned_str = re.sub(r'^```(json)?\\\
          s*|\\s*```$', '', raw_str.strip())\n\n            # 一个字符串可能包含多个由换行符分隔的JSON对象\n\
          \            potential_json_strs = cleaned_str.split('\\n')\n\n        \
          \    for json_str in potential_json_strs:\n                # 跳过空行\n    \
          \            if not json_str.strip():\n                    continue\n\n\
          \                try:\n                    # 解析JSON字符串为Python字典\n      \
          \              data_obj = json.loads(json_str)\n\n                    #\
          \ 创建一个规范化的字符串表示形式（通过排序键）用于唯一性检查\n                    canonical_repr = json.dumps(data_obj,\
          \ sort_keys=True)\n\n                    # 核心逻辑：\n                    #\
          \ 如果对象是新的 (不存在于 processed_objects 中),\n                    # 就添加它和它的来源。\n\
          \                    # 由于我们先处理 arg1 (\"main\")，如果一个对象同时存在于 arg1 和 arg2，\n\
          \                    # 它在处理 arg2 时会被跳过，从而保留 \"main\" 作为其来源。\n          \
          \          if canonical_repr not in processed_objects:\n               \
          \         data_obj[\"source\"] = source\n                        processed_objects[canonical_repr]\
          \ = data_obj\n\n                except json.JSONDecodeError:\n         \
          \           # 如果某行不是有效的JSON，则忽略它，以增强代码的鲁棒性\n                    # print(f\"\
          警告: 无法解析以下内容为JSON: {json_str}\")\n                    pass\n\n    # 1. 首先处理\
          \ arg1, 来源标记为 \"main\"\n    parse_and_process(arg1, \"main\")\n\n    # 2.\
          \ 接着处理 arg2, 来源标记为 \"tfl\"\n    parse_and_process(arg2, \"tfl\")\n\n   \
          \ # 3. 从字典的值中提取最终的列表\n    final_list = list(processed_objects.values())\n\
          \n    return {\"result\": final_list}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[object]
        selected: false
        title: 获取提取到的所有缩略语
        type: code
        variables:
        - value_selector:
          - '1732535749965'
          - output
          value_type: array[string]
          variable: arg1
        - value_selector:
          - '17536734555410'
          - output
          value_type: array[string]
          variable: arg2
      height: 54
      id: '1753673470605'
      position:
        x: 3481.061018958151
        y: 502.05376187053724
      positionAbsolute:
        x: 3481.061018958151
        y: 502.05376187053724
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(arg1: list[str], arg2: list[str]) -> dict:\n    return {'result':\
          \ ''.join(arg1) + ''.join(arg2)}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 把所有正文拼起来
        type: code
        variables:
        - value_selector:
          - '1753669997187'
          - main_content
          value_type: array[string]
          variable: arg1
        - value_selector:
          - '1753669997187'
          - tfl_table_content
          value_type: array[string]
          variable: arg2
      height: 54
      id: '17536744443700'
      position:
        x: 593.2333696992415
        y: 875.7818437860288
      positionAbsolute:
        x: 593.2333696992415
        y: 875.7818437860288
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "\ndef main() -> dict:\n    abbr=[\n    {\n      \"abbreviation\": \"\
          ADR\",\n      \"full_form\": \"Adverse Drug Reaction\",\n      \"definition_cn\"\
          : \"药物不良反应\"\n    },\n    {\n      \"abbreviation\": \"AE\",\n      \"full_form\"\
          : \"Adverse Event\",\n      \"definition_cn\": \"不良事件\"\n    },\n    {\n\
          \      \"abbreviation\": \"ALB\",\n      \"full_form\": \"Albumin\",\n \
          \     \"definition_cn\": \"白蛋白\"\n    },\n    {\n      \"abbreviation\"\
          : \"ALP\",\n      \"full_form\": \"Alkaline Phosphatase\",\n      \"definition_cn\"\
          : \"碱性磷酸酶\"\n    },\n    {\n      \"abbreviation\": \"ALT\",\n      \"full_form\"\
          : \"Alanine Transaminase\",\n      \"definition_cn\": \"丙氨酸氨基转氨酶/丙氨酸转氨酶\"\
          \n    },\n    {\n      \"abbreviation\": \"AST\",\n      \"full_form\":\
          \ \"Aspartate Transaminase\",\n      \"definition_cn\": \"天冬氨酸氨基转氨酶/天冬氨酸转氨酶\"\
          \n    },\n    {\n      \"abbreviation\": \"BMI\",\n      \"full_form\":\
          \ \"Body Mass Index\",\n      \"definition_cn\": \"体重指数\"\n    },\n    {\n\
          \      \"abbreviation\": \"CI\",\n      \"full_form\": \"Confidence Interval\"\
          ,\n      \"definition_cn\": \"置信区间\"\n    },\n    {\n      \"abbreviation\"\
          : \"CL\",\n      \"full_form\": \"Clearance\",\n      \"definition_cn\"\
          : \"表观清除率\"\n    },\n    {\n      \"abbreviation\": \"Cmax\",\n      \"\
          full_form\": \"Maximum Concentration\",\n      \"definition_cn\": \"峰浓度\"\
          \n    },\n    {\n      \"abbreviation\": \"Cmin\",\n      \"full_form\"\
          : \"Minimum Value Of Concentration\",\n      \"definition_cn\": \"谷浓度\"\n\
          \    },\n    {\n      \"abbreviation\": \"Cr\",\n      \"full_form\": \"\
          Creatinine\",\n      \"definition_cn\": \"肌酐\"\n    },\n    {\n      \"\
          abbreviation\": \"CRA\",\n      \"full_form\": \"Clinical Research Associate\"\
          ,\n      \"definition_cn\": \"临床监察员\"\n    },\n    {\n      \"abbreviation\"\
          : \"CV\",\n      \"full_form\": \"Coefficient of Variation\",\n      \"\
          definition_cn\": \"变异系数\"\n    },\n    {\n      \"abbreviation\": \"DBiL\"\
          ,\n      \"full_form\": \"Direct Bilirubin\",\n      \"definition_cn\":\
          \ \"直接胆红素\"\n    },\n    {\n      \"abbreviation\": \"DM\",\n      \"full_form\"\
          : \"Data Manager\",\n      \"definition_cn\": \"数据管理员\"\n    },\n    {\n\
          \      \"abbreviation\": \"DMP\",\n      \"full_form\": \"data Management\
          \ Plan\",\n      \"definition_cn\": \"数据管理计划\"\n    },\n    {\n      \"\
          abbreviation\": \"DVP\",\n      \"full_form\": \"Data Validation Plan\"\
          ,\n      \"definition_cn\": \"数据核查计划\"\n    },\n    {\n      \"abbreviation\"\
          : \"Dyn\",\n      \"full_form\": \"Dynorphin\",\n      \"definition_cn\"\
          : \"强啡肽\"\n    },\n    {\n      \"abbreviation\": \"ECG\",\n      \"full_form\"\
          : \"Electrocardiogram\",\n      \"definition_cn\": \"心电图\"\n    },\n   \
          \ {\n      \"abbreviation\": \"eCRF\",\n      \"full_form\": \"electronic\
          \ Case Report Form\",\n      \"definition_cn\": \"电子病例报告表\"\n    },\n  \
          \  {\n      \"abbreviation\": \"EDC\",\n      \"full_form\": \"Electronic\
          \ Data Capture\",\n      \"definition_cn\": \"电子信息采集系统\"\n    },\n    {\n\
          \      \"abbreviation\": \"FDA\",\n      \"full_form\": \"Food and Drug\
          \ Administration\",\n      \"definition_cn\": \"美国食品及药物管理局\"\n    },\n \
          \   {\n      \"abbreviation\": \"FSH\",\n      \"full_form\": \"Follicle\
          \ Stimulating Hormone\",\n      \"definition_cn\": \"卵泡刺激素\"\n    },\n \
          \   {\n      \"abbreviation\": \"GCP\",\n      \"full_form\": \"Good Clinical\
          \ Practice\",\n      \"definition_cn\": \"药物临床试验质量管理规范\"\n    },\n    {\n\
          \      \"abbreviation\": \"GLU\",\n      \"full_form\": \"Glucose\",\n \
          \     \"definition_cn\": \"血葡萄糖\"\n    },\n    {\n      \"abbreviation\"\
          : \"GnRH\",\n      \"full_form\": \"Gonadotropin-releasing Hormone\",\n\
          \      \"definition_cn\": \"促性腺激素释放激素\"\n    },\n    {\n      \"abbreviation\"\
          : \"HBcAb\",\n      \"full_form\": \"Hepatitis B Core Antibody\",\n    \
          \  \"definition_cn\": \"乙肝核心抗体\"\n    },\n    {\n      \"abbreviation\"\
          : \"HBeAg\",\n      \"full_form\": \"Hepatitis B Envelope Antigen\",\n \
          \     \"definition_cn\": \"乙肝e抗原\"\n    },\n    {\n      \"abbreviation\"\
          : \"HBsAg\",\n      \"full_form\": \"Hepatitis B Surface Antigen\",\n  \
          \    \"definition_cn\": \"乙肝表面抗原\"\n    },\n    {\n      \"abbreviation\"\
          : \"HCV Ab\",\n      \"full_form\": \"Hepatitis C Virus Antibody\",\n  \
          \    \"definition_cn\": \"丙型肝炎病毒抗体\"\n    },\n    {\n      \"abbreviation\"\
          : \"HCT\",\n      \"full_form\": \"Hematocrit\",\n      \"definition_cn\"\
          : \"红细胞压积\"\n    },\n    {\n      \"abbreviation\": \"HGB\",\n      \"full_form\"\
          : \"Hemoglobin\",\n      \"definition_cn\": \"血红蛋白\"\n    },\n    {\n  \
          \    \"abbreviation\": \"HRT\",\n      \"full_form\": \"Hormone Replacement\
          \ Therapy\",\n      \"definition_cn\": \"激素替代治疗\"\n    },\n    {\n     \
          \ \"abbreviation\": \"Hy’s law\",\n      \"full_form\": \"Hy’s law\",\n\
          \      \"definition_cn\": \"海氏法则\"\n    },\n    {\n      \"abbreviation\"\
          : \"ICF\",\n      \"full_form\": \"Informed Consent Form\",\n      \"definition_cn\"\
          : \"知情同意书\"\n    },\n    {\n      \"abbreviation\": \"Kiss\",\n      \"\
          full_form\": \"Kisspeptin\",\n      \"definition_cn\": \"下丘脑神经肽\"\n    },\n\
          \    {\n      \"abbreviation\": \"KNDy\",\n      \"full_form\": \"Kisspeptin\
          \ / Neurokinkin B / Dynorphin\",\n      \"definition_cn\": \"神经肽-神经激肽B-强啡肽\"\
          \n    },\n    {\n      \"abbreviation\": \"LDH\",\n      \"full_form\":\
          \ \"Lactic Dehydrogenase\",\n      \"definition_cn\": \"乳酸脱氢酶\"\n    },\n\
          \    {\n      \"abbreviation\": \"LH\",\n      \"full_form\": \"Luteinizing\
          \ Hormone\",\n      \"definition_cn\": \"黄体生成素\"\n    },\n    {\n      \"\
          abbreviation\": \"NKB\",\n      \"full_form\": \"Neurokinkin B\",\n    \
          \  \"definition_cn\": \"神经激肽B\"\n    },\n    {\n      \"abbreviation\":\
          \ \"NK3R\",\n      \"full_form\": \"Neurokinkin 3R\",\n      \"definition_cn\"\
          : \"神经激肽3受体\"\n    },\n    {\n      \"abbreviation\": \"PA\",\n      \"\
          full_form\": \"Pharmacologically Active\",\n      \"definition_cn\": \"\
          药理学活性\"\n    },\n    {\n      \"abbreviation\": \"PD\",\n      \"full_form\"\
          : \"Pharmacodynamics\",\n      \"definition_cn\": \"药效学\"\n    },\n    {\n\
          \      \"abbreviation\": \"PDAS\",\n      \"full_form\": \"Pharmacodynamics\
          \ Analysis Set\",\n      \"definition_cn\": \"药效学分析集\"\n    },\n    {\n\
          \      \"abbreviation\": \"PDYN\",\n      \"full_form\": \"Prodynorphin\"\
          ,\n      \"definition_cn\": \"原激肽\"\n    },\n    {\n      \"abbreviation\"\
          : \"PK\",\n      \"full_form\": \"Pharmacokinetics\",\n      \"definition_cn\"\
          : \"药代动力学\"\n    },\n    {\n      \"abbreviation\": \"PKAS\",\n      \"\
          full_form\": \"Pharmacokinetics Analysis Set\",\n      \"definition_cn\"\
          : \"药代动力学分析集\"\n    },\n    {\n      \"abbreviation\": \"PLT\",\n      \"\
          full_form\": \"Blood platelet\",\n      \"definition_cn\": \"血小板\"\n   \
          \ },\n    {\n      \"abbreviation\": \"RBC\",\n      \"full_form\": \"Red\
          \ Blood Cell Count\",\n      \"definition_cn\": \"红细胞计数\"\n    },\n    {\n\
          \      \"abbreviation\": \"SAE\",\n      \"full_form\": \"Serious Adverse\
          \ Event\",\n      \"definition_cn\": \"严重不良事件\"\n    },\n    {\n      \"\
          abbreviation\": \"SAP\",\n      \"full_form\": \"Statistical Analysis Plan\"\
          ,\n      \"definition_cn\": \"统计分析计划\"\n    },\n    {\n      \"abbreviation\"\
          : \"SBP\",\n      \"full_form\": \"Systolic Blood Pressure\",\n      \"\
          definition_cn\": \"收缩压\"\n    },\n    {\n      \"abbreviation\": \"SD\"\
          ,\n      \"full_form\": \"Standard Deviation\",\n      \"definition_cn\"\
          : \"标准差\"\n    },\n    {\n      \"abbreviation\": \"SOC\",\n      \"full_form\"\
          : \"System Organ Class\",\n      \"definition_cn\": \"系统器官分类\"\n    },\n\
          \    {\n      \"abbreviation\": \"SRC\",\n      \"full_form\": \"Safety\
          \ Review Committee\",\n      \"definition_cn\": \"安全审核委员会\"\n    },\n  \
          \  {\n      \"abbreviation\": \"SS\",\n      \"full_form\": \"Safety Set\"\
          ,\n      \"definition_cn\": \"安全分析集\"\n    },\n    {\n      \"abbreviation\"\
          : \"SUSAR\",\n      \"full_form\": \"Suspected Unexpected Serious Adverse\
          \ Reaction\",\n      \"definition_cn\": \"可疑非预期严重不良反应\"\n    },\n    {\n\
          \      \"abbreviation\": \"Testo\",\n      \"full_form\": \"Testosterone\"\
          ,\n      \"definition_cn\": \"睾酮\"\n    },\n    {\n      \"abbreviation\"\
          : \"t1/2\",\n      \"full_form\": \"Half-life\",\n      \"definition_cn\"\
          : \"半衰期\"\n    },\n    {\n      \"abbreviation\": \"TBIL\",\n      \"full_form\"\
          : \"Total Bilirubin\",\n      \"definition_cn\": \"总胆红素\"\n    },\n    {\n\
          \      \"abbreviation\": \"TEAE\",\n      \"full_form\": \"Treatment Emergent\
          \ Adverse Event\",\n      \"definition_cn\": \"给药期出现的不良事件\"\n    },\n  \
          \  {\n      \"abbreviation\": \"TG\",\n      \"full_form\": \"Triglyceride\"\
          ,\n      \"definition_cn\": \"甘油三酯\"\n    },\n    {\n      \"abbreviation\"\
          : \"Tmax\",\n      \"full_form\": \"Time to Peak\",\n      \"definition_cn\"\
          : \"达峰时间\"\n    },\n    {\n      \"abbreviation\": \"TP\",\n      \"full_form\"\
          : \"Total Protein\",\n      \"definition_cn\": \"总蛋白\"\n    },\n    {\n\
          \      \"abbreviation\": \"ULN\",\n      \"full_form\": \"Upper Limit of\
          \ Normal\",\n      \"definition_cn\": \"正常值上限\"\n    },\n    {\n      \"\
          abbreviation\": \"Vd\",\n      \"full_form\": \"Apparent Volume of Distribution\"\
          ,\n      \"definition_cn\": \"表观分布容积\"\n    },\n    {\n      \"abbreviation\"\
          : \"VMS\",\n      \"full_form\": \"Vasomotor symptoms\",\n      \"definition_cn\"\
          : \"血管舒缩症状\"\n    },\n    {\n      \"abbreviation\": \"WBC\",\n      \"\
          full_form\": \"White Blood Cell Count\",\n      \"definition_cn\": \"白细胞计数\"\
          \n    },\n    {\n      \"abbreviation\": \"AMD\",\n      \"full_form\":\
          \ \"Age-related Macular Degeneration\",\n      \"definition_cn\": \"年龄相关性黄斑变性\"\
          \n    },\n    {\n      \"abbreviation\": \"BM\",\n      \"full_form\": \"\
          Bruch membrane\",\n      \"definition_cn\": \"突破玻璃膜\"\n    },\n    {\n \
          \     \"abbreviation\": \"CNV\",\n      \"full_form\": \"Choroidal neovascularization\"\
          ,\n      \"definition_cn\": \"黄斑区脉络膜新生血管\"\n    },\n    {\n      \"abbreviation\"\
          : \"DR\",\n      \"full_form\": \"Diabetic Retinopathy\",\n      \"definition_cn\"\
          : \"糖尿病视网膜病变\"\n    },\n    {\n      \"abbreviation\": \"DCP\",\n      \"\
          full_form\": \"Deep capillary plexus\",\n      \"definition_cn\": \"深层毛细血管丛\"\
          \n    },\n    {\n      \"abbreviation\": \"FAS\",\n      \"full_form\":\
          \ \"Full Analysis Set\",\n      \"definition_cn\": \"全分析集\"\n    },\n  \
          \  {\n      \"abbreviation\": \"FAZ\",\n      \"full_form\": \"Foveal Avascular\
          \ Zone\",\n      \"definition_cn\": \"中心凹无血管区\"\n    },\n    {\n      \"\
          abbreviation\": \"FDA\",\n      \"full_form\": \"Food and Drug Administration\"\
          ,\n      \"definition_cn\": \"美国食品药品监督管理局\"\n    },\n    {\n      \"abbreviation\"\
          : \"FFA\",\n      \"full_form\": \"Fundus fluorescein angiography\",\n \
          \     \"definition_cn\": \"荧光素眼底血管造影术\"\n    },\n    {\n      \"abbreviation\"\
          : \"ICNV\",\n      \"full_form\": \"Idiopathic Choroidal Neovascularization\"\
          ,\n      \"definition_cn\": \"特发性脉络膜新生血管\"\n    },\n    {\n      \"abbreviation\"\
          : \"ITT\",\n      \"full_form\": \"Intention to treat\",\n      \"definition_cn\"\
          : \"意向性\"\n    },\n    {\n      \"abbreviation\": \"IRMA\",\n      \"full_form\"\
          : \"Intraretinal microvascular abnormality\",\n      \"definition_cn\":\
          \ \"视网膜内微血管异常\"\n    },\n    {\n      \"abbreviation\": \"LSO\",\n     \
          \ \"full_form\": \"Line Scanning Ophthalmoscope\",\n      \"definition_cn\"\
          : \"线扫描检眼镜\"\n    },\n    {\n      \"abbreviation\": \"MA\",\n      \"full_form\"\
          : \"Microaneurysm\",\n      \"definition_cn\": \"微动脉瘤\"\n    },\n    {\n\
          \      \"abbreviation\": \"NMPA\",\n      \"full_form\": \"National Medical\
          \ Products Administration\",\n      \"definition_cn\": \"国家药品监督管理局\"\n \
          \   },\n    {\n      \"abbreviation\": \"OCT\",\n      \"full_form\": \"\
          Optical Coherence Tomography\",\n      \"definition_cn\": \"光相干断层扫描\"\n\
          \    },\n    {\n      \"abbreviation\": \"OCTA\",\n      \"full_form\":\
          \ \"OCT angiography\",\n      \"definition_cn\": \"相干断层扫描血管成像\"\n    },\n\
          \    {\n      \"abbreviation\": \"PCV\",\n      \"full_form\": \"Polypoidal\
          \ Choroidal Vasculopathy\",\n      \"definition_cn\": \"息肉样脉络膜血管病变\"\n \
          \   },\n    {\n      \"abbreviation\": \"PDT\",\n      \"full_form\": \"\
          Photodynamic Therapy\",\n      \"definition_cn\": \"过光动力疗法\"\n    },\n \
          \   {\n      \"abbreviation\": \"PI\",\n      \"full_form\": \"Principal\
          \ Investigator\",\n      \"definition_cn\": \"主要研究者\"\n    },\n    {\n \
          \     \"abbreviation\": \"PPS\",\n      \"full_form\": \"Per Protocol Set\"\
          ,\n      \"definition_cn\": \"符合方案分析集\"\n    },\n    {\n      \"abbreviation\"\
          : \"Q1\",\n      \"full_form\": \"Lower quartile\",\n      \"definition_cn\"\
          : \"下四分位数\"\n    },\n    {\n      \"abbreviation\": \"Q3\",\n      \"full_form\"\
          : \"Upper quartile\",\n      \"definition_cn\": \"上四分位数\"\n    },\n    {\n\
          \      \"abbreviation\": \"RPE\",\n      \"full_form\": \"Retinal pigment\
          \ epithelium\",\n      \"definition_cn\": \"视网膜色素上皮层\"\n    },\n    {\n\
          \      \"abbreviation\": \"RVO\",\n      \"full_form\": \"Retinal vein occlusion\"\
          ,\n      \"definition_cn\": \"视网膜静脉阻塞\"\n    },\n    {\n      \"abbreviation\"\
          : \"SD-OCT\",\n      \"full_form\": \"Spectral-Domain OCT\",\n      \"definition_cn\"\
          : \"频域OCT\"\n    },\n    {\n      \"abbreviation\": \"SMD\",\n      \"full_form\"\
          : \"Senile macular degeneration\",\n      \"definition_cn\": \"老年性黄斑变性\"\
          \n    },\n    {\n      \"abbreviation\": \"SS\",\n      \"full_form\": \"\
          Safety Set\",\n      \"definition_cn\": \"安全性分析集\"\n    },\n    {\n    \
          \  \"abbreviation\": \"SS-OCT\",\n      \"full_form\": \"Swept-Source OCT\"\
          ,\n      \"definition_cn\": \"扫频光源OCT\"\n    },\n    {\n      \"abbreviation\"\
          : \"WOCF\",\n      \"full_form\": \"Worst Observation Carried Forward\"\
          ,\n      \"definition_cn\": \"最差填补法\"\n    },\n    {\n      \"abbreviation\"\
          : \"CRO\",\n      \"full_form\": \"Contract Research Organization\",\n \
          \     \"definition_cn\": \"合同研究组织\"\n    },\n    {\n      \"abbreviation\"\
          : \"EDC\",\n      \"full_form\": \"Electronic Data Capture\",\n      \"\
          definition_cn\": \"电子数据采集系统\"\n    },\n    {\n      \"abbreviation\": \"\
          eCRF\",\n      \"full_form\": \"Electronic Case Report Form\",\n      \"\
          definition_cn\": \"电子病例报告表\"\n    },\n    {\n      \"abbreviation\": \"\
          GCP\",\n      \"full_form\": \"Good Clinical Practice\",\n      \"definition_cn\"\
          : \"临床试验管理规范\"\n    },\n    {\n      \"abbreviation\": \"HIS\",\n      \"\
          full_form\": \"Hospital Information System\",\n      \"definition_cn\":\
          \ \"医院信息系统\"\n    },\n    {\n      \"abbreviation\": \"SDV\",\n      \"\
          full_form\": \"Source Data Verification\",\n      \"definition_cn\": \"\
          原始数据核对\"\n    },\n    {\n      \"abbreviation\": \"SOP\",\n      \"full_form\"\
          : \"Standard Operation Procedure\",\n      \"definition_cn\": \"标准操作流程\"\
          \n    },\n    {\n      \"abbreviation\": \"ALT\",\n      \"full_form\":\
          \ \"Alanine Aminotransferase\",\n      \"definition_cn\": \"丙氨酸氨基转移酶\"\n\
          \    },\n    {\n      \"abbreviation\": \"APTT\",\n      \"full_form\":\
          \ \"Activated Partial Thromboplastin Time\",\n      \"definition_cn\": \"\
          活化部分凝血活酶时间\"\n    },\n    {\n      \"abbreviation\": \"AST\",\n      \"\
          full_form\": \"Aspartate Transaminase\",\n      \"definition_cn\": \"天门冬氨酸氨基转移酶\"\
          \n    },\n    {\n      \"abbreviation\": \"BUN\",\n      \"full_form\":\
          \ \"Blood Urea Nitrogen\",\n      \"definition_cn\": \"尿素氮\"\n    },\n \
          \   {\n      \"abbreviation\": \"Cr\",\n      \"full_form\": \"Creatinine\"\
          ,\n      \"definition_cn\": \"血清肌酐\"\n    },\n    {\n      \"abbreviation\"\
          : \"CRA\",\n      \"full_form\": \"Clinical Research Associate\",\n    \
          \  \"definition_cn\": \"临床监查员\"\n    },\n    {\n      \"abbreviation\":\
          \ \"EC\",\n      \"full_form\": \"Ethical Committee\",\n      \"definition_cn\"\
          : \"伦理委员会\"\n    },\n    {\n      \"abbreviation\": \"eCRF\",\n      \"\
          full_form\": \"eCase Report Form\",\n      \"definition_cn\": \"电子病例报告表\"\
          \n    },\n    {\n      \"abbreviation\": \"EDC\",\n      \"full_form\":\
          \ \"Electronic Data Capture\",\n      \"definition_cn\": \"电子数据采集\"\n  \
          \  },\n    {\n      \"abbreviation\": \"FIB\",\n      \"full_form\": \"\
          Fibrinogen\",\n      \"definition_cn\": \"纤维蛋白原\"\n    },\n    {\n     \
          \ \"abbreviation\": \"GAIS\",\n      \"full_form\": \"Global Aesthetic Improvement\
          \ Scale\",\n      \"definition_cn\": \"整体美观改善效果评价量表\"\n    },\n    {\n \
          \     \"abbreviation\": \"GCP\",\n      \"full_form\": \"Good Clinical Practice\"\
          ,\n      \"definition_cn\": \"临床试验质量管理规范\"\n    },\n    {\n      \"abbreviation\"\
          : \"GLU\",\n      \"full_form\": \"Blood Glucose\",\n      \"definition_cn\"\
          : \"血糖\"\n    },\n    {\n      \"abbreviation\": \"ICH\",\n      \"full_form\"\
          : \"International Conference on Harmonization\",\n      \"definition_cn\"\
          : \"国际协调会议\"\n    },\n    {\n      \"abbreviation\": \"INR\",\n      \"\
          full_form\": \"International Normalized Ratio\",\n      \"definition_cn\"\
          : \"国际标准化比值\"\n    },\n    {\n      \"abbreviation\": \"NLFs\",\n      \"\
          full_form\": \"Nasolabial Folds\",\n      \"definition_cn\": \"鼻唇沟\"\n \
          \   },\n    {\n      \"abbreviation\": \"PLT\",\n      \"full_form\": \"\
          Platelet Count\",\n      \"definition_cn\": \"血小板计数\"\n    },\n    {\n \
          \     \"abbreviation\": \"PPS\",\n      \"full_form\": \"Per Protocol Set\"\
          ,\n      \"definition_cn\": \"符合方案集\"\n    },\n    {\n      \"abbreviation\"\
          : \"PT\",\n      \"full_form\": \"Prothrombin Time\",\n      \"definition_cn\"\
          : \"凝血酶原时间\"\n    },\n    {\n      \"abbreviation\": \"SDV\",\n      \"\
          full_form\": \"Source Data Verification\",\n      \"definition_cn\": \"\
          源数据核查确认\"\n    },\n    {\n      \"abbreviation\": \"SS\",\n      \"full_form\"\
          : \"Safety Set\",\n      \"definition_cn\": \"安全性数据集\"\n    },\n    {\n\
          \      \"abbreviation\": \"WSRS\",\n      \"full_form\": \"Wrinkle Severity\
          \ Rating Scale\",\n      \"definition_cn\": \"皱纹严重程度分级量表\"\n    },\n   \
          \ {\n      \"abbreviation\": \"AUC0\",\n      \"full_form\": \"Curve from\
          \ Zero up to ∞ with Extrapolation of the Terminal Phase\",\n      \"definition_cn\"\
          : \"从0时到无穷大时间的药物浓度-时间曲线下面积\"\n    },\n    {\n      \"abbreviation\": \"\
          AUC0t\",\n      \"full_form\": \"Area Under the Concentration-Time Curve\
          \ From Zero up to a Definite Time T\",\n      \"definition_cn\": \"从0时到最后一个浓度可准确测定的样品采集时间t的药物浓度-时间曲线下面积\"\
          \n    },\n    {\n      \"abbreviation\": \"A/G\",\n      \"full_form\":\
          \ \"Albumin/globulin ratio\",\n      \"definition_cn\": \"白球比\"\n    },\n\
          \    {\n      \"abbreviation\": \"AC\",\n      \"full_form\": \"Adenylate\
          \ cyclase\",\n      \"definition_cn\": \"腺苷酸环化酶\"\n    },\n    {\n     \
          \ \"abbreviation\": \"ACE\",\n      \"full_form\": \"Angiotensin-converting\
          \ enzyme\",\n      \"definition_cn\": \"血管紧张素转换酶\"\n    },\n    {\n    \
          \  \"abbreviation\": \"ADA\",\n      \"full_form\": \"American Diabetes\
          \ Association\",\n      \"definition_cn\": \"美国糖尿病协会\"\n    },\n    {\n\
          \      \"abbreviation\": \"ADL\",\n      \"full_form\": \"Activities of\
          \ Daily Living\",\n      \"definition_cn\": \"日常生活活动\"\n    },\n    {\n\
          \      \"abbreviation\": \"AE\",\n      \"full_form\": \"Adverse event\"\
          ,\n      \"definition_cn\": \"不良事件\"\n    },\n    {\n      \"abbreviation\"\
          : \"ALP\",\n      \"full_form\": \"Alkaline phosphatase\",\n      \"definition_cn\"\
          : \"碱性磷酸酶\"\n    },\n    {\n      \"abbreviation\": \"ALT\",\n      \"full_form\"\
          : \"Alanine aminotransferase\",\n      \"definition_cn\": \"谷氨酸天冬氨酸氨基转移酶\"\
          \n    },\n    {\n      \"abbreviation\": \"ApoB\",\n      \"full_form\"\
          : \"Apolipoprotein B\",\n      \"definition_cn\": \"载脂蛋白B\"\n    },\n  \
          \  {\n      \"abbreviation\": \"ARB\",\n      \"full_form\": \"Angiotensin\
          \ receptor blockers\",\n      \"definition_cn\": \"血管紧张素受体阻滞剂\"\n    },\n\
          \    {\n      \"abbreviation\": \"AST\",\n      \"full_form\": \"Aspartate\
          \ aminotransferase\",\n      \"definition_cn\": \"谷氨酸丙氨酸氨基转移酶\"\n    },\n\
          \    {\n      \"abbreviation\": \"AUC0-t\",\n      \"full_form\": \"Area\
          \ under the plasma concentration time curve\",\n      \"definition_cn\"\
          : \"血药浓度-时间曲线下面积\"\n    },\n    {\n      \"abbreviation\": \"BAT\",\n  \
          \    \"full_form\": \"Brown adipose tissue\",\n      \"definition_cn\":\
          \ \"棕色脂肪组织\"\n    },\n    {\n      \"abbreviation\": \"BMI\",\n      \"\
          full_form\": \"Body mass index\",\n      \"definition_cn\": \"体重指数\"\n \
          \   },\n    {\n      \"abbreviation\": \"BP\",\n      \"full_form\": \"\
          Blood pressure\",\n      \"definition_cn\": \"血压\"\n    },\n    {\n    \
          \  \"abbreviation\": \"BT\",\n      \"full_form\": \"Body temperature\"\
          ,\n      \"definition_cn\": \"体温\"\n    },\n    {\n      \"abbreviation\"\
          : \"cAMP\",\n      \"full_form\": \"Cyclic adenosine monophosphate\",\n\
          \      \"definition_cn\": \"环磷酸腺苷\"\n    },\n    {\n      \"abbreviation\"\
          : \"CFB\",\n      \"full_form\": \"Change from baseline\",\n      \"definition_cn\"\
          : \"较基线的变化\"\n    },\n    {\n      \"abbreviation\": \"CK18\",\n      \"\
          full_form\": \"Cytokeratin 18\",\n      \"definition_cn\": \"细胞角蛋白18\"\n\
          \    },\n    {\n      \"abbreviation\": \"CL/F\",\n      \"full_form\":\
          \ \"Apparent clearance\",\n      \"definition_cn\": \"表观清除率\"\n    },\n\
          \    {\n      \"abbreviation\": \"Cmax\",\n      \"full_form\": \"Maximum\
          \ concentration\",\n      \"definition_cn\": \"达峰浓度\"\n    },\n    {\n \
          \     \"abbreviation\": \"CRF\",\n      \"full_form\": \"Case report form\"\
          ,\n      \"definition_cn\": \"病例报告表\"\n    },\n    {\n      \"abbreviation\"\
          : \"CRU\",\n      \"full_form\": \"Clinical research unit\",\n      \"definition_cn\"\
          : \"临床试验中心\"\n    },\n    {\n      \"abbreviation\": \"Css-max\",\n    \
          \  \"full_form\": \"Maximum concentration at steady status\",\n      \"\
          definition_cn\": \"稳态峰浓度\"\n    },\n    {\n      \"abbreviation\": \"CV\"\
          ,\n      \"full_form\": \"Coefficient of variation\",\n      \"definition_cn\"\
          : \"变异系数\"\n    },\n    {\n      \"abbreviation\": \"CVD\",\n      \"full_form\"\
          : \"Cardiovascular risk disease\",\n      \"definition_cn\": \"心血管疾病\"\n\
          \    },\n    {\n      \"abbreviation\": \"DPP-4\",\n      \"full_form\"\
          : \"Dipeptidyl peptidase-4 inhibitor\",\n      \"definition_cn\": \"二肽基肽酶4\"\
          \n    },\n    {\n      \"abbreviation\": \"EASD\",\n      \"full_form\"\
          : \"European Association for the Study of Diabetes\",\n      \"definition_cn\"\
          : \"欧洲糖尿病研究协会\"\n    },\n    {\n      \"abbreviation\": \"EDP\",\n     \
          \ \"full_form\": \"Drug exposure during pregnancy\",\n      \"definition_cn\"\
          : \"妊娠暴露\"\n    },\n    {\n      \"abbreviation\": \"EIU\",\n      \"full_form\"\
          : \"Exposure in-utero\",\n      \"definition_cn\": \"子宫暴露\"\n    },\n  \
          \  {\n      \"abbreviation\": \"FAS\",\n      \"full_form\": \"Full analysis\
          \ set\",\n      \"definition_cn\": \"全分析集\"\n    },\n    {\n      \"abbreviation\"\
          : \"FE\",\n      \"full_form\": \"Food effect\",\n      \"definition_cn\"\
          : \"食物影响\"\n    },\n    {\n      \"abbreviation\": \"FES\",\n      \"full_form\"\
          : \"Food effect set\",\n      \"definition_cn\": \"食物影响分析集合\"\n    },\n\
          \    {\n      \"abbreviation\": \"FGF21\",\n      \"full_form\": \"Fibroblast\
          \ growth factor 21\",\n      \"definition_cn\": \"成纤维细胞生长因子21\"\n    },\n\
          \    {\n      \"abbreviation\": \"FPG\",\n      \"full_form\": \"Fasting\
          \ plasma glucose\",\n      \"definition_cn\": \"空腹血糖\"\n    },\n    {\n\
          \      \"abbreviation\": \"FPI\",\n      \"full_form\": \"Fasting plasma\
          \ insulin\",\n      \"definition_cn\": \"空腹血浆胰岛素\"\n    },\n    {\n    \
          \  \"abbreviation\": \"FSH\",\n      \"full_form\": \"Follicle-stimulating\
          \ homone\",\n      \"definition_cn\": \"促卵泡激素\"\n    },\n    {\n      \"\
          abbreviation\": \"GCP\",\n      \"full_form\": \"Good Clinical Proctice\"\
          ,\n      \"definition_cn\": \"药物临床研究质量管理规范\"\n    },\n    {\n      \"abbreviation\"\
          : \"GLP\",\n      \"full_form\": \"Good Laboratory Practice\",\n      \"\
          definition_cn\": \"药物非临床研究质量管理规范\"\n    },\n    {\n      \"abbreviation\"\
          : \"GLP-1\",\n      \"full_form\": \"Glucagon-like peptide-1\",\n      \"\
          definition_cn\": \"胰高血糖素样肽1\"\n    },\n    {\n      \"abbreviation\": \"\
          GLP-1R\",\n      \"full_form\": \"GLP-1 receptor\",\n      \"definition_cn\"\
          : \"胰高血糖素样肽-1受体\"\n    },\n    {\n      \"abbreviation\": \"GLP-1RA\",\n\
          \      \"full_form\": \"GLP-1R agonist\",\n      \"definition_cn\": \"GLP-1R激动剂\"\
          \n    },\n    {\n      \"abbreviation\": \"GMP\",\n      \"full_form\":\
          \ \"Good Manufacturing Practice\",\n      \"definition_cn\": \"药品生产质量管理规范\"\
          \n    },\n    {\n      \"abbreviation\": \"GPCR\",\n      \"full_form\"\
          : \"Gprotein-coupled receptor\",\n      \"definition_cn\": \"G蛋白偶联受体\"\n\
          \    },\n    {\n      \"abbreviation\": \"GSIS\",\n      \"full_form\":\
          \ \"Glucose stimulates insulin secretion\",\n      \"definition_cn\": \"\
          葡萄糖刺激的胰岛素分泌\"\n    },\n    {\n      \"abbreviation\": \"HbA1c\",\n     \
          \ \"full_form\": \"Hemoglobin A1c\",\n      \"definition_cn\": \"糖化血红蛋白\"\
          \n    },\n    {\n      \"abbreviation\": \"HDL-C\",\n      \"full_form\"\
          : \"High density liptein cholesterol\",\n      \"definition_cn\": \"高密度脂蛋白胆固醇\"\
          \n    },\n    {\n      \"abbreviation\": \"HED\",\n      \"full_form\":\
          \ \"Human Equvalent Dose\",\n      \"definition_cn\": \"人体等效剂量\"\n    },\n\
          \    {\n      \"abbreviation\": \"HLGT\",\n      \"full_form\": \"High-Level\
          \ group term\",\n      \"definition_cn\": \"高级别组术语\"\n    },\n    {\n  \
          \    \"abbreviation\": \"HLT\",\n      \"full_form\": \"High-Level term\"\
          ,\n      \"definition_cn\": \"高等级术语\"\n    },\n    {\n      \"abbreviation\"\
          : \"HOMA\",\n      \"full_form\": \"Homeostatic model assessment\",\n  \
          \    \"definition_cn\": \"稳态模型评估\"\n    },\n    {\n      \"abbreviation\"\
          : \"HR\",\n      \"full_form\": \"Heart rate\",\n      \"definition_cn\"\
          : \"心率\"\n    },\n    {\n      \"abbreviation\": \"ICF\",\n      \"full_form\"\
          : \"Informed consent form\",\n      \"definition_cn\": \"知情同意书\"\n    },\n\
          \    {\n      \"abbreviation\": \"ICH\",\n      \"full_form\": \"International\
          \ Conference on Harmonization\",\n      \"definition_cn\": \"人用药品注册技术国际协调会\"\
          \n    },\n    {\n      \"abbreviation\": \"ID\",\n      \"full_form\": \"\
          Identification number\",\n      \"definition_cn\": \"识别编号\"\n    },\n  \
          \  {\n      \"abbreviation\": \"IP3\",\n      \"full_form\": \"Inositol\
          \ trisphosphate\",\n      \"definition_cn\": \"三磷酸肌醇\"\n    },\n    {\n\
          \      \"abbreviation\": \"IPGTT\",\n      \"full_form\": \"Intraperitoneal\
          \ glucose tolerance test\",\n      \"definition_cn\": \"腹腔葡萄糖耐量试验\"\n  \
          \  },\n    {\n      \"abbreviation\": \"IRB\",\n      \"full_form\": \"\
          Institutional review board\",\n      \"definition_cn\": \"伦理委员会\"\n    },\n\
          \    {\n      \"abbreviation\": \"LDL-c\",\n      \"full_form\": \"Low density\
          \ lipoprotein cholesterol\",\n      \"definition_cn\": \"低密度脂蛋白胆固醇\"\n \
          \   },\n    {\n      \"abbreviation\": \"LLT\",\n      \"full_form\": \"\
          Lower-Level term\",\n      \"definition_cn\": \"低等级术语\"\n    },\n    {\n\
          \      \"abbreviation\": \"Lp(a)\",\n      \"full_form\": \"Lipoprotein\
          \ a\",\n      \"definition_cn\": \"脂蛋白a\"\n    },\n    {\n      \"abbreviation\"\
          : \"LYMP\",\n      \"full_form\": \"Lymphocyte\",\n      \"definition_cn\"\
          : \"淋巴细胞\"\n    },\n    {\n      \"abbreviation\": \"MD\",\n      \"full_form\"\
          : \"Multiple doses\",\n      \"definition_cn\": \"多次给药\"\n    },\n    {\n\
          \      \"abbreviation\": \"MAD\",\n      \"full_form\": \"Multiple ascending\
          \ doses\",\n      \"definition_cn\": \"多次剂量递增\"\n    },\n    {\n      \"\
          abbreviation\": \"MDG\",\n      \"full_form\": \"Mean daily glucose\",\n\
          \      \"definition_cn\": \"平均每日血糖\"\n    },\n    {\n      \"abbreviation\"\
          : \"MedDRA\",\n      \"full_form\": \"Medical Dictionary for Regulatory\
          \ Activities\",\n      \"definition_cn\": \"国际医学用语词典\"\n    },\n    {\n\
          \      \"abbreviation\": \"MEN2\",\n      \"full_form\": \"Multiple endocrineneoplasia\
          \ 2\",\n      \"definition_cn\": \"2型多发内分泌肿瘤综合征\"\n    },\n    {\n     \
          \ \"abbreviation\": \"MRI\",\n      \"full_form\": \"Magnetic resonance\
          \ imaging\",\n      \"definition_cn\": \"磁共振成像\"\n    },\n    {\n      \"\
          abbreviation\": \"MRSD\",\n      \"full_form\": \"Maximum recommended starting\
          \ dose\",\n      \"definition_cn\": \"最大推荐起始剂量\"\n    },\n    {\n      \"\
          abbreviation\": \"MTC\",\n      \"full_form\": \"Medullary thyroid carcinoma\"\
          ,\n      \"definition_cn\": \"甲状腺髓样癌\"\n    },\n    {\n      \"abbreviation\"\
          : \"ND\",\n      \"full_form\": \"Not detectable\",\n      \"definition_cn\"\
          : \"无法定量\"\n    },\n    {\n      \"abbreviation\": \"NEUT\",\n      \"full_form\"\
          : \"Neutrophil\",\n      \"definition_cn\": \"中性粒细胞\"\n    },\n    {\n \
          \     \"abbreviation\": \"NOAEL\",\n      \"full_form\": \"No observed adverse\
          \ effect level\",\n      \"definition_cn\": \"未见不良反应剂量水平\"\n    },\n   \
          \ {\n      \"abbreviation\": \"NMPA\",\n      \"full_form\": \"National\
          \ Medical Products Administration\",\n      \"definition_cn\": \"中国国家药品监督管理局\"\
          \n    },\n    {\n      \"abbreviation\": \"PD\",\n      \"full_form\": \"\
          Pharmacodynamics\",\n      \"definition_cn\": \"药效动力学\"\n    },\n    {\n\
          \      \"abbreviation\": \"PDFF\",\n      \"full_form\": \"Proton density\
          \ fat fraction\",\n      \"definition_cn\": \"质子密度脂肪分数\"\n    },\n    {\n\
          \      \"abbreviation\": \"PDS\",\n      \"full_form\": \"PD set\",\n  \
          \    \"definition_cn\": \"药效学分析集\"\n    },\n    {\n      \"abbreviation\"\
          : \"PKA\",\n      \"full_form\": \"Protein kinase A\",\n      \"definition_cn\"\
          : \"蛋白激酶A\"\n    },\n    {\n      \"abbreviation\": \"PKCS\",\n      \"\
          full_form\": \"PK concentration set\",\n      \"definition_cn\": \"药代动力学浓度分析集\"\
          \n    },\n    {\n      \"abbreviation\": \"PKPS\",\n      \"full_form\"\
          : \"PK parameter set\",\n      \"definition_cn\": \"药代动力学参数分析集\"\n    },\n\
          \    {\n      \"abbreviation\": \"PT\",\n      \"full_form\": \"Preferred\
          \ term\",\n      \"definition_cn\": \"首选语\"\n    },\n    {\n      \"abbreviation\"\
          : \"QTc\",\n      \"full_form\": \"Corrected QT interval\",\n      \"definition_cn\"\
          : \"校正QT间期\"\n    },\n    {\n      \"abbreviation\": \"RET\",\n      \"\
          full_form\": \"Reticulocyte\",\n      \"definition_cn\": \"网织红细胞\"\n   \
          \ },\n    {\n      \"abbreviation\": \"RPR\",\n      \"full_form\": \"Rapid\
          \ plasma regain test\",\n      \"definition_cn\": \"快速血浆反应素环状卡片试验\"\n  \
          \  },\n    {\n      \"abbreviation\": \"RR\",\n      \"full_form\": \"Respiratory\
          \ rate\",\n      \"definition_cn\": \"呼吸频率\"\n    },\n    {\n      \"abbreviation\"\
          : \"RyR\",\n      \"full_form\": \"Ryanodine receptor\",\n      \"definition_cn\"\
          : \"兰尼碱受体\"\n    },\n    {\n      \"abbreviation\": \"SAD\",\n      \"full_form\"\
          : \"Single ascending doses\",\n      \"definition_cn\": \"单次剂量递增\"\n   \
          \ },\n    {\n      \"abbreviation\": \"SAE\",\n      \"full_form\": \"Serious\
          \ adverse event\",\n      \"definition_cn\": \"严重不良事件\"\n    },\n    {\n\
          \      \"abbreviation\": \"SOC\",\n      \"full_form\": \"System organ class\"\
          ,\n      \"definition_cn\": \"系统器官分类\"\n    },\n    {\n      \"abbreviation\"\
          : \"SS\",\n      \"full_form\": \"Safety set\",\n      \"definition_cn\"\
          : \"安全性分析集\"\n    },\n    {\n      \"abbreviation\": \"SUSAR\",\n      \"\
          full_form\": \"Suspicious and unexpected serious adverse reactions\",\n\
          \      \"definition_cn\": \"可疑且非预期严重不良反应\"\n    },\n    {\n      \"abbreviation\"\
          : \"t1/2\",\n      \"full_form\": \"Elimination half life\",\n      \"definition_cn\"\
          : \"消除半衰期\"\n    },\n    {\n      \"abbreviation\": \"T2DM\",\n      \"\
          full_form\": \"Type 2 diabetes mellitus\",\n      \"definition_cn\": \"\
          2型糖尿病\"\n    },\n    {\n      \"abbreviation\": \"TBIL\",\n      \"full_form\"\
          : \"Total bilirubin\",\n      \"definition_cn\": \"总胆红素\"\n    },\n    {\n\
          \      \"abbreviation\": \"TC\",\n      \"full_form\": \"Total cholesterol\"\
          ,\n      \"definition_cn\": \"总胆固醇\"\n    },\n    {\n      \"abbreviation\"\
          : \"TEAE\",\n      \"full_form\": \"Treatment emergent adverse events\"\
          ,\n      \"definition_cn\": \"治疗中出现的不良事件\"\n    },\n    {\n      \"abbreviation\"\
          : \"Tmax\",\n      \"full_form\": \"Peak time\",\n      \"definition_cn\"\
          : \"达峰时间\"\n    },\n    {\n      \"abbreviation\": \"Vdss\",\n      \"full_form\"\
          : \"Volume of distribution at steady state\",\n      \"definition_cn\":\
          \ \"平均稳态表观分布容积\"\n    },\n    {\n      \"abbreviation\": \"ULN\",\n    \
          \  \"full_form\": \"Upper limits of normal\",\n      \"definition_cn\":\
          \ \"正常范围上限\"\n    },\n    {\n      \"abbreviation\": \"WAT\",\n      \"\
          full_form\": \"White adipose tissue\",\n      \"definition_cn\": \"白色脂肪组织\"\
          \n    },\n    {\n      \"abbreviation\": \"WBC\",\n      \"full_form\":\
          \ \"White blood cell\",\n      \"definition_cn\": \"白细胞\"\n    },\n    {\n\
          \      \"abbreviation\": \"WHO\",\n      \"full_form\": \"World Health Organization\"\
          ,\n      \"definition_cn\": \"世界卫生组织\"\n    },\n    {\n      \"abbreviation\"\
          : \"ADR\",\n      \"full_form\": \"Adverse drug reaction\",\n      \"definition_cn\"\
          : \"药物不良反应\"\n    },\n    {\n      \"abbreviation\": \"AIS\",\n      \"\
          full_form\": \"Acute ischemic stroke\",\n      \"definition_cn\": \"急性缺血性脑卒中\"\
          \n    },\n    {\n      \"abbreviation\": \"ALT\",\n      \"full_form\":\
          \ \"Alanine aminotransferase\",\n      \"definition_cn\": \"丙氨酸氨基转移酶\"\n\
          \    },\n    {\n      \"abbreviation\": \"AST\",\n      \"full_form\": \"\
          Aspartate aminotransferase\",\n      \"definition_cn\": \"天门冬氨酸氨基转移酶\"\n\
          \    },\n    {\n      \"abbreviation\": \"ATC\",\n      \"full_form\": \"\
          Anatomical Therapeutic Chemical\",\n      \"definition_cn\": \"解剖学治疗学及化学\"\
          \n    },\n    {\n      \"abbreviation\": \"BI\",\n      \"full_form\": \"\
          Barthel index\",\n      \"definition_cn\": \"Barthel 指数量表\"\n    },\n  \
          \  {\n      \"abbreviation\": \"CI\",\n      \"full_form\": \"Confidence\
          \ Internal\",\n      \"definition_cn\": \"置信区间\"\n    },\n    {\n      \"\
          abbreviation\": \"Cmax\",\n      \"full_form\": \"Peak Concentration\",\n\
          \      \"definition_cn\": \"达峰浓度\"\n    },\n    {\n      \"abbreviation\"\
          : \"CRF\",\n      \"full_form\": \"Case Report Form\",\n      \"definition_cn\"\
          : \"病例报告表\"\n    },\n    {\n      \"abbreviation\": \"CT\",\n      \"full_form\"\
          : \"Computed Tomography\",\n      \"definition_cn\": \"计算机断层扫描\"\n    },\n\
          \    {\n      \"abbreviation\": \"CTR\",\n      \"full_form\": \"Clinical\
          \ Trials Registration\",\n      \"definition_cn\": \"临床试验登记\"\n    },\n\
          \    {\n      \"abbreviation\": \"ECASS\",\n      \"full_form\": \"European\
          \ Cooperative Acute Stroke Study\",\n      \"definition_cn\": \"欧洲急性卒中协作研究\"\
          \n    },\n    {\n      \"abbreviation\": \"EVT\",\n      \"full_form\":\
          \ \"Endovascular thrombectomy\",\n      \"definition_cn\": \"血管内血栓切除术\"\n\
          \    },\n    {\n      \"abbreviation\": \"hsCRP\",\n      \"full_form\"\
          : \"high sensitivity CRP\",\n      \"definition_cn\": \"超敏C 反应蛋白\"\n   \
          \ },\n    {\n      \"abbreviation\": \"ICH\",\n      \"full_form\": \"International\
          \ Conference on Harmonization\",\n      \"definition_cn\": \"国际人用药品注册技术协调会议\"\
          \n    },\n    {\n      \"abbreviation\": \"ICH\",\n      \"full_form\":\
          \ \"asymptomatic intracranial hemorrhage\",\n      \"definition_cn\": \"\
          无症状性颅内出血\"\n    },\n    {\n      \"abbreviation\": \"IEC\",\n      \"full_form\"\
          : \"Independent Ethics Committee\",\n      \"definition_cn\": \"独立伦理委员会\"\
          \n    },\n    {\n      \"abbreviation\": \"IL\",\n      \"full_form\": \"\
          Interleukin\",\n      \"definition_cn\": \"白细胞介素\"\n    },\n    {\n    \
          \  \"abbreviation\": \"ITT\",\n      \"full_form\": \"Intention To Treat\"\
          ,\n      \"definition_cn\": \"意向治疗\"\n    },\n    {\n      \"abbreviation\"\
          : \"IWRS\",\n      \"full_form\": \"Interactive Web Response System\",\n\
          \      \"definition_cn\": \"交互式网络应答系统\"\n    },\n    {\n      \"abbreviation\"\
          : \"MCP-1\",\n      \"full_form\": \"Monocyte Chemoattractant Protein-1\"\
          ,\n      \"definition_cn\": \"单核细胞趋化蛋白-1\"\n    },\n    {\n      \"abbreviation\"\
          : \"MedDRA\",\n      \"full_form\": \"Medical Dictionary for Regulatory\
          \ Activities\",\n      \"definition_cn\": \"监管活动医学词典\"\n    },\n    {\n\
          \      \"abbreviation\": \"MRI\",\n      \"full_form\": \"Nuclear Magnetic\
          \ Resonance Imaging\",\n      \"definition_cn\": \"核磁共振成像\"\n    },\n  \
          \  {\n      \"abbreviation\": \"mRS\",\n      \"full_form\": \"Modified\
          \ Rankin Scale\",\n      \"definition_cn\": \"改良Rankin 量表\"\n    },\n  \
          \  {\n      \"abbreviation\": \"NA\",\n      \"full_form\": \"Not Applicable\"\
          ,\n      \"definition_cn\": \"不适用\"\n    },\n    {\n      \"abbreviation\"\
          : \"NE\",\n      \"full_form\": \"Not Evaluable\",\n      \"definition_cn\"\
          : \"无法评估\"\n    },\n    {\n      \"abbreviation\": \"NIHSS\",\n      \"\
          full_form\": \"National Institute of Health stroke scale\",\n      \"definition_cn\"\
          : \"美国国立卫生研究院卒中量表\"\n    },\n    {\n      \"abbreviation\": \"NMPA\",\n\
          \      \"full_form\": \"National medical Product Adiministration\",\n  \
          \    \"definition_cn\": \"国家药品监督管理局\"\n    },\n    {\n      \"abbreviation\"\
          : \"PT\",\n      \"full_form\": \"Preferred Term\",\n      \"definition_cn\"\
          : \"首选术语\"\n    },\n    {\n      \"abbreviation\": \"PK\",\n      \"full_form\"\
          : \"Pharmacokinetic\",\n      \"definition_cn\": \"药代动力学\"\n    },\n   \
          \ {\n      \"abbreviation\": \"Q1，Q3\",\n      \"full_form\": \"Quartile\
          \ 1，Quartile 3\",\n      \"definition_cn\": \"第一四分位数，第三四分位数\"\n    },\n\
          \    {\n      \"abbreviation\": \"RR\",\n      \"full_form\": \"relative\
          \ risk\",\n      \"definition_cn\": \"相对危险度\"\n    },\n    {\n      \"abbreviation\"\
          : \"rtPA\",\n      \"full_form\": \"Recombinant Tissue Plasminogen Activator\"\
          ,\n      \"definition_cn\": \"重组组织型纤维蛋白溶解酶原激活剂\"\n    },\n    {\n      \"\
          abbreviation\": \"sICH\",\n      \"full_form\": \"symptomatic intracranial\
          \ hemorrhage\",\n      \"definition_cn\": \"症状性颅内出血\"\n    },\n    {\n \
          \     \"abbreviation\": \"SOP\",\n      \"full_form\": \"Standard Operating\
          \ Procedure\",\n      \"definition_cn\": \"标准操作规程\"\n    },\n    {\n   \
          \   \"abbreviation\": \"SS\",\n      \"full_form\": \"Safety Analysis Set\"\
          ,\n      \"definition_cn\": \"安全性分析集\"\n    },\n    {\n      \"abbreviation\"\
          : \"t1/2\",\n      \"full_form\": \"Half Life\",\n      \"definition_cn\"\
          : \"半衰期\"\n    },\n    {\n      \"abbreviation\": \"TEAE\",\n      \"full_form\"\
          : \"Treatment-Emergent Adverse Event\",\n      \"definition_cn\": \"治疗期不良事件\"\
          \n    },\n    {\n      \"abbreviation\": \"TFL\",\n      \"full_form\":\
          \ \"Table，Figure，Listing\",\n      \"definition_cn\": \"表格，图表，列表\"\n   \
          \ },\n    {\n      \"abbreviation\": \"TRAE\",\n      \"full_form\": \"\
          Treatment-Related Adverse Event\",\n      \"definition_cn\": \"治疗相关不良事件\"\
          \n    },\n    {\n      \"abbreviation\": \"WHODrug Global\",\n      \"full_form\"\
          : \"World Health Organization Drug Dictionaries\",\n      \"definition_cn\"\
          : \"WHO 药物词典\"\n    }\n  ]\n    return {\n        \"result\": abbr,\n  \
          \  }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[object]
        selected: false
        title: 放所有的正确的术语进行干预
        type: code
        variables: []
      height: 54
      id: '1753674630551'
      position:
        x: 1816.2464412048237
        y: 875.7818437860288
      positionAbsolute:
        x: 1816.2464412048237
        y: 875.7818437860288
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\nimport re\nfrom typing import Mapping, Sequence\n\n\n\
          def has_chinese(text: str) -> bool:\n    \"\"\"检查文本是否包含中文字符\"\"\"\n    if\
          \ not isinstance(text, str):\n        return False\n    return bool(re.search(r\"\
          [\\u4e00-\\u9fff]\", text))\n\n\ndef has_english(text: str) -> bool:\n \
          \   \"\"\"检查文本是否包含英文字符\"\"\"\n    if not isinstance(text, str):\n      \
          \  return False\n    return bool(re.search(r\"[a-zA-Z]\", text))\n\n\ndef\
          \ main(\n    arg1: Sequence[Mapping[str, object]], arg2: str, arg3: Sequence[Mapping[str,\
          \ object]]\n) -> dict[str, list[dict[str, object]]]:\n    \"\"\"\n    对\
          \ arg1 中的每个英文简称：\n      1. 在 arg2 文本中统计出现次数；\n      2. 在 arg3 术语规范中查找同名项，并用\
          \ arg3 的信息规范化对应字段；\n\n    返回格式：保持 arg1 的原始结构，添加 count 字段，如果在 arg3 中找到匹配项则用\
          \ arg3 的信息规范化\n    {\n      \"result\": [\n        {\n          \"中文术语\"\
          : \"...\",\n          \"英文全称\": \"...\",\n          \"英文简称\": \"...\",\n\
          \          \"source\": \"...\",\n          \"count\": 123\n        },\n\
          \        ...\n      ]\n    }\n    \"\"\"\n    # 构建 arg3 的索引：abbreviation\
          \ -> 完整的术语信息\n    norm_map: dict[str, dict[str, object]] = {\n        item[\"\
          abbreviation\"]: {\"full_form\": item[\"full_form\"], \"definition_cn\"\
          : item[\"definition_cn\"]}\n        for item in arg3\n        if \"abbreviation\"\
          \ in item and isinstance(item[\"abbreviation\"], str)\n    }\n\n    results:\
          \ list[dict[str, object]] = []\n\n    for entry in arg1:\n        # 复制原始条目的所有字段\n\
          \        result_entry = dict(entry)\n\n        abbr = entry.get(\"英文简称\"\
          ) or entry.get(\"abbreviation\")\n        if not isinstance(abbr, str):\n\
          \            continue\n\n        # 全词匹配，忽略大小写\n        pattern = rf\"\\\
          b{re.escape(abbr)}\\b\"\n        count = len(re.findall(pattern, arg2, flags=re.IGNORECASE))\n\
          \n        # 添加计数字段\n        result_entry[\"count\"] = count\n\n        #\
          \ 规范化：如果在 arg3 中找到匹配项，用 arg3 的信息规范化\n        spec = norm_map.get(abbr)\n\
          \        if spec:\n            result_entry[\"英文全称\"] = spec[\"full_form\"\
          ]\n            result_entry[\"中文术语\"] = spec[\"definition_cn\"]\n\n    \
          \    results.append(result_entry)\n\n    # 去重：基于英文简称去重，优先保留 source=\"main\"\
          \ 的条目\n    abbreviation_map = {}\n    for entry in results:\n        abbr\
          \ = entry.get(\"英文简称\")\n        if abbr:\n            if abbr not in abbreviation_map:\n\
          \                # 第一次遇到这个简称，直接添加\n                abbreviation_map[abbr]\
          \ = entry\n            elif entry.get(\"source\") == \"main\" and abbreviation_map[abbr].get(\"\
          source\") != \"main\":\n                # 如果当前条目是 main 来源，而已存在的不是，则替换\n\
          \                abbreviation_map[abbr] = entry\n            # 如果已存在的是 main\
          \ 来源，或者当前条目不是 main 来源，则保持不变\n\n    unique_results = list(abbreviation_map.values())\n\
          \n    # 过滤掉 count 为 0 的条目（在文本中没有出现的术语）\n    filtered_results = [entry for\
          \ entry in unique_results if entry.get(\"count\", 0) > 0]\n\n    # 过滤掉异常值：中文术语没有中文字符，或英文简称没有英文字符\n\
          \    valid_results = []\n    for entry in filtered_results:\n        chinese_term\
          \ = entry.get(\"中文术语\", \"\")\n        english_abbr = entry.get(\"英文简称\"\
          , \"\")\n\n        # 检查中文术语是否包含中文字符\n        if not has_chinese(chinese_term):\n\
          \            continue\n\n        # 检查英文简称是否包含英文字符\n        if not has_english(english_abbr):\n\
          \            continue\n\n        valid_results.append(entry)\n\n    # 按\
          \ count 降序排序\n    valid_results.sort(key=lambda x: x[\"count\"], reverse=True)\
          \  # type: ignore\n\n    return {\"result\": valid_results}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[object]
        selected: false
        title: 最终输出
        type: code
        variables:
        - value_selector:
          - '1753673470605'
          - result
          value_type: array[object]
          variable: arg1
        - value_selector:
          - '17536744443700'
          - result
          value_type: string
          variable: arg2
        - value_selector:
          - '1753674630551'
          - result
          value_type: array[object]
          variable: arg3
      height: 52
      id: '1753675304191'
      position:
        x: 3798.8979478205315
        y: 707.9896005806944
      positionAbsolute:
        x: 3798.8979478205315
        y: 707.9896005806944
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1753675304191'
          - result
          value_type: array[object]
          variable: result
        selected: false
        title: 结束
        type: end
      height: 88
      id: '1753684498784'
      position:
        x: 4101.897947820531
        y: 707.9896005806944
      positionAbsolute:
        x: 4101.897947820531
        y: 707.9896005806944
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        selected: false
        template: '{{ arg1 }}'
        title: 模板转换
        type: template-transform
        variables:
        - value_selector:
          - '1732535749965'
          - output
          value_type: array[string]
          variable: arg1
      height: 52
      id: '1753686202400'
      position:
        x: 2759.370234494195
        y: 122.13362884993035
      positionAbsolute:
        x: 2759.370234494195
        y: 122.13362884993035
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        selected: false
        template: '{{ arg1 }}'
        title: 模板转换 2
        type: template-transform
        variables:
        - value_selector:
          - '17536734555410'
          - output
          value_type: array[string]
          variable: arg1
      height: 52
      id: '1753686211748'
      position:
        x: 2807.2437359436412
        y: 409.51625311673195
      positionAbsolute:
        x: 2807.2437359436412
        y: 409.51625311673195
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    viewport:
      x: -1193.5573866849938
      y: 234.77904486628586
      zoom: 0.5727751484875678
