import json
import re


def merge_and_deduplicate_terms(arg1: list[str], arg2: list[str]) -> dict:
    """
    解析、合并、去重并标记来源两个列表中的JSON字符串。

    Args:
      arg1: 来源为 "main" 的原始字符串列表。
      arg2: 来源为 "tfl" 的原始字符串列表。

    Returns:
      一个包含 "result" 键的字典，其值为处理后的对象列表。
    """

    # 使用字典来存储唯一的对象，键是对象的规范化字符串表示，值是带来源信息的对象本身。
    # 这样可以高效地去重并实现来源优先逻辑。
    processed_objects = {}

    def parse_and_process(raw_strings: list[str], source: str):
        """
        一个内部辅助函数，用于解析字符串列表并填充 processed_objects 字典。
        """
        for raw_str in raw_strings:
            # 移除常见的Markdown代码块标记
            cleaned_str = re.sub(r"^```(json)?\s*|\s*```$", "", raw_str.strip())

            # 一个字符串可能包含多个由换行符分隔的JSON对象
            potential_json_strs = cleaned_str.split("\n")

            for json_str in potential_json_strs:
                # 跳过空行
                if not json_str.strip():
                    continue

                try:
                    # 解析JSON字符串为Python字典
                    data_obj = json.loads(json_str)

                    # 创建一个规范化的字符串表示形式（通过排序键）用于唯一性检查
                    canonical_repr = json.dumps(data_obj, sort_keys=True)

                    # 核心逻辑：
                    # 如果对象是新的 (不存在于 processed_objects 中),
                    # 就添加它和它的来源。
                    # 由于我们先处理 arg1 ("main")，如果一个对象同时存在于 arg1 和 arg2，
                    # 它在处理 arg2 时会被跳过，从而保留 "main" 作为其来源。
                    if canonical_repr not in processed_objects:
                        data_obj["source"] = source
                        processed_objects[canonical_repr] = data_obj

                except json.JSONDecodeError:
                    # 如果某行不是有效的JSON，则忽略它，以增强代码的鲁棒性
                    # print(f"警告: 无法解析以下内容为JSON: {json_str}")
                    pass

    # 1. 首先处理 arg1, 来源标记为 "main"
    parse_and_process(arg1, "main")

    # 2. 接着处理 arg2, 来源标记为 "tfl"
    parse_and_process(arg2, "tfl")

    # 3. 从字典的值中提取最终的列表
    final_list = list(processed_objects.values())

    return {"result": final_list}


# 您提供的输入数据
input_data = {
    "arg1": [
    "```json\n{\"中文术语\": \"Polo样蛋白激酶1\", \"英文全称\": \"Polo-Like Kinase 1\", \"英文简称\": \"PLK1\"}\n```",
    "```json\n{\"中文术语\": \"晶型A\", \"英文全称\": \"Crystal Form A\", \"英文简称\": \"A\"}\n```",
    "```json\n{\"中文术语\": \"全分析集\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"有效性分析集\", \"英文全称\": \"Efficacy Analysis Set\", \"英文简称\": \"EAS\"}\n```json\n{\"中文术语\": \"安全性分析集\", \"英文全称\": \"Safety Set\", \"英文简称\": \"SS\"}\n```json\n{\"中文术语\": \"DLT分析集\", \"英文全称\": \"Dose Limiting Toxicity Analysis Set\", \"英文简称\": \"DLTS\"}\n```",
    "```json\n{\"中文术语\": \"药代动力学浓度集\", \"英文全称\": \"Pharmacokinetic Concentration Set\", \"英文简称\": \"PKCS\"}\n{\"中文术语\": \"药代动力学参数集\", \"英文全称\": \"Pharmacokinetic Parameter Set\", \"英文简称\": \"PKPS\"}\n{\"中文术语\": \"C-QTc分析集\", \"英文全称\": \"Concentration-QTc Analysis Set\", \"英文简称\": \"C-QTcS\"}\n```",
    "```json\n{\"中文术语\": \"全分析集\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n{\"中文术语\": \"有效性分析集\", \"英文全称\": \"Efficacy Analysis Set\", \"英文简称\": \"EAS\"}\n```",
    "```json\n{\"中文术语\": \"安全性分析集\", \"英文全称\": \"Safety Set\", \"英文简称\": \"SS\"}\n{\"中文术语\": \"DLT分析集\", \"英文全称\": \"Dose Limiting Toxicity Set\", \"英文简称\": \"DLTS\"}\n{\"中文术语\": \"药代动力学浓度集\", \"英文全称\": \"PK concentration set\", \"英文简称\": \"PKCS\"}\n{\"中文术语\": \"药代动力学参数集\", \"英文全称\": \"PK parameter set\", \"英文简称\": \"PKPS\"}\n```",
    "```json\n{\"中文术语\": \"C-QTc分析集\", \"英文全称\": \"C-QTc Set\", \"英文简称\": \"C-QTcS\"}\n```json\n{\"中文术语\": \"全分析集\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```json\n{\"中文术语\": \"有效性分析集\", \"英文全称\": \"Efficacy Analysis Set\", \"英文简称\": \"EAS\"}\n```json\n{\"中文术语\": \"安全性分析集\", \"英文全称\": \"Safety Set\", \"英文简称\": \"SS\"}\n```json\n{\"中文术语\": \"剂量限制毒性分析集\", \"英文全称\": \"Dose Limiting Toxicity Set\", \"英文简称\": \"DLTS\"}\n```json\n{\"中文术语\": \"药代动力学浓度分析集\", \"英文全称\": \"PK concentration set\", \"英文简称\": \"PKCS\"}\n```json\n{\"中文术语\": \"药代动力学参数分析集\", \"英文全称\": \"PK parameter set\", \"英文简称\": \"PKPS\"}\n```",
    "```json\n{\"中文术语\": \"全分析集\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```json\n{\"中文术语\": \"有效性分析集\", \"英文全称\": \"Effectiveness Analysis Set\", \"英文简称\": \"EAS\"}\n```",
    "```json\n{\"中文术语\": \"安全性分析集\", \"英文全称\": \"Safety Set\", \"英文简称\": \"SS\"}\n```json\n{\"中文术语\": \"DLT分析集\", \"英文全称\": \"Dose Limiting Toxicity Set\", \"英文简称\": \"DLTS\"}\n```",
    "```json\n{\"中文术语\": \"药代动力学浓度集\", \"英文全称\": \"PK concentration set\", \"英文简称\": \"PKCS\"}\n{\"中文术语\": \"药代动力学参数集\", \"英文全称\": \"PK parameter set\", \"英文简称\": \"PKPS\"}\n{\"中文术语\": \"C-QTc分析集\", \"英文全称\": \"C-QTc set\", \"英文简称\": \"C-QTcS\"}\n```"
  ],
    "arg2": [
    "```json\n{\"中文术语\": \"各患者（按肿瘤类型分）疗效评价游泳图\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"不同肿瘤类型的最佳总疗效\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"有进展生存曲线\", \"英文全称\": \"survival curve\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"既往抗肿瘤治疗史\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"各患者（按剂量组分）疗效评价游泳图\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"分析数据集汇总\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"重大方案偏离汇总\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"患者分布\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"不同剂量组治疗后的最佳总体疗效\", \"英文全称\": \"Full Analysis Set\", \"英文简称\": \"FAS\"}\n```",
    "```json\n{\"中文术语\": \"无进展生存期\", \"英文全称\": \"Progression-Free Survival\", \"英文简称\": \"PFS\"}\n```json\n{\"中文术语\": \"评估分析集\", \"英文全称\": \"Efficacy Analysis Set\", \"英文简称\": \"EAS\"}\n```json\n{\"中文术语\": \"研究日\", \"英文全称\": \"Study Day\", \"英文简称\": \"\"}\n```json\n{\"中文术语\": \"受试者筛选号\", \"英文全称\": \"Subject Screening Number\", \"英文简称\": \"\"}\n```json\n{\"中文术语\": \"首次给药日期\", \"英文全称\": \"First Dose Date\", \"英文简称\": \"\"}\n```json\n{\"中文术语\": \"研究后抗肿瘤治疗\", \"英文全称\": \"Post-study Antitumor Therapy\", \"英文简称\": \"\"}\n```json\n{\"中文术语\": \"事件/删失日期\", \"英文全称\": \"Event/Censoring Date\", \"英文简称\": \"\"}\n```json\n{\"中文术语\": \"事件/删失\", \"英文全称\": \"Event/Censoring\", \"英文简称\": \"\"}\n```json\n{\"中文术语\": \"事件/删失描述\", \"英文全称\": \"Event/Censoring Description\", \"英文简称\": \"\"}\n```json\n{\"中文术语\": \"评估/检查日期\", \"英文全称\": \"Assessment/Examination Date\", \"英文简称\": \"\"}\n```",
    "```json\n{\"中文术语\": \"药代动力学血药浓度总结表\", \"英文全称\": \"Pharmacokinetic concentration summary table\", \"英文简称\": \"PK\"}\n```json\n{\"中文术语\": \"药代动力学浓度分析集\", \"英文全称\": \"PK concentration set\", \"英文简称\": \"PKCS\"}\n```",
    "```json\n{\"中文术语\": \"系统器官分类\", \"英文全称\": \"System Organ Class\", \"英文简称\": \"XS-03\"}\n```",
    "```json\n{\"中文术语\": \"无进展生存期\", \"英文全称\": \"Progression-Free Survival\", \"英文简称\": \"PFS\"}\n```json\n{\"中文术语\": \"药代动力学浓度分析集\", \"英文全称\": \"PK concentration set\", \"英文简称\": \"PKCS\"}\n```json\n{\"中文术语\": \"药代动力学参数分析集\", \"英文全称\": \"PK parameter set\", \"英文简称\": \"PKPS\"}\n```",
    "```json\n{\"中文术语\": \"严重不良事件\", \"英文全称\": \"Serious Adverse Event\", \"英文简称\": \"SAE\"}\n{\"中文术语\": \"系统器官分类\", \"英文全称\": \"System Organ Class\", \"英文简称\": \"SOC\"}\n{\"中文术语\": \"首选术语\", \"英文全称\": \"Preferred Term\", \"英文简称\": \"PT\"}\n{\"中文术语\": \"监管活动医学词典\", \"英文全称\": \"Medical Dictionary for Regulatory Activities\", \"英文简称\": \"MedDRA\"}\n```",
    "```json\n{\"中文术语\": \"系统器官分类\", \"英文全称\": \"System Organ Class\", \"英文简称\": \"SOC\"}\n{\"中文术语\": \"首选术语\", \"英文全称\": \"Preferred Term\", \"英文简称\": \"PT\"}\n{\"中文术语\": \"严重不良事件\", \"英文全称\": \"Serious Adverse Event\", \"英文简称\": \"SAE\"}\n{\"中文术语\": \"监管活动医学词典\", \"英文全称\": \"Medical Dictionary for Regulatory Activities\", \"英文简称\": \"MedDRA\"}\n```",
    "```json\n{\"中文术语\": \"总生存期\", \"英文全称\": \"Overall Survival\", \"英文简称\": \"OS\"}\n```json\n{\"中文术语\": \"EAS集\", \"英文全称\": \"Efficacy Analysis Set\", \"英文简称\": \"EAS\"}\n```",
    "```json\n{\"中文术语\": \"系统器官分类\", \"英文全称\": \"System Organ Class\", \"英文简称\": \"SOC\"}\n```json\n{\"中文术语\": \"首选术语\", \"英文全称\": \"Preferred Term\", \"英文简称\": \"PT\"}\n```json\n{\"中文术语\": \"严重不良事件\", \"英文全称\": \"Serious Adverse Event\", \"英文简称\": \"SAE\"}\n```json\n{\"中文术语\": \"不良事件通用术语标准\", \"英文全称\": \"Common Terminology Criteria for Adverse Events\", \"英文简称\": \"CTCAE\"}\n```json\n{\"中文术语\": \"监管活动医学词典\", \"英文全称\": \"Medical Dictionary for Regulatory Activities\", \"英文简称\": \"MedDRA\"}\n```",
    "```json\n{\"中文术语\": \"总生存期\", \"英文全称\": \"Overall Survival\", \"英文简称\": \"OS\"}\n```json\n{\"中文术语\": \"EAS集\", \"英文全称\": \"Efficacy Analysis Set\", \"英文简称\": \"EAS\"}\n```",
    "```json\n{\"中文术语\": \"系统器官分类\", \"英文全称\": \"System Organ Class\", \"英文简称\": \"SOC\"}\n```json\n{\"中文术语\": \"首选术语\", \"英文全称\": \"Preferred Term\", \"英文简称\": \"PT\"}\n```json\n{\"中文术语\": \"严重不良事件\", \"英文全称\": \"Serious Adverse Event\", \"英文简称\": \"SAE\"}\n```json\n{\"中文术语\": \"监管活动医学词典\", \"英文全称\": \"Medical Dictionary for Regulatory Activities\", \"英文简称\": \"MedDRA\"}\n```json\n{\"中文术语\": \"不良事件通用术语标准\", \"英文全称\": \"Common Terminology Criteria for Adverse Events\", \"英文简称\": \"CTCAE\"}\n```"
  ],
}

# 调用函数处理数据
final_output = merge_and_deduplicate_terms(input_data["arg1"], input_data["arg2"])

# 打印格式化后的JSON输出 (ensure_ascii=False确保中文字符正确显示, indent=2使其更易读)
print(json.dumps(final_output, ensure_ascii=False, indent=2))
